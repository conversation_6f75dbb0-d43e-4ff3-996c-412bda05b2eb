import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

import { prodLogger } from '../../../utils/prodLogger.js';
const BillingSection = () => {
  const { user, profile, loading: authLoading, updateProfile } = useAuth();
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState(null);
  const [showBillingAddress, setShowBillingAddress] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Billing address form state
  const [billingAddress, setBillingAddress] = useState({
    name: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: ''
  });

  // Load billing address from profile
  useEffect(() => {
    if (profile) {
      setBillingAddress({
        name: profile.full_name || '',
        address: profile.billing_address || '',
        city: profile.billing_city || '',
        state: profile.billing_state || '',
        country: profile.billing_country || '',
        postalCode: profile.billing_postal_code || ''
      });
    }
  }, [profile]);

  // Helper function to get plan name from subscription tier
  const getPlanName = (subscriptionTier) => {
    const planMap = {
      'free': 'Free Plan',
      'pro': 'Professional Plan', 
      'enterprise': 'Enterprise Plan'
    };
    return planMap[subscriptionTier] || 'Free Plan';
  };

  // Helper function to get plan price
  const getPlanPrice = (subscriptionTier) => {
    const priceMap = {
      'free': '₦0',
      'pro': '₦15,000',
      'enterprise': '₦35,000'
    };
    return priceMap[subscriptionTier] || '₦0';
  };

  const [paymentMethods] = useState([
    {
      id: 1,
      type: 'card',
      brand: 'visa',
      last4: '4242',
      expiryMonth: 12,
      expiryYear: 2025,
      isDefault: true
    },
    {
      id: 2,
      type: 'bank',
      bankName: 'First Bank of Nigeria',
      accountNumber: '****1234',
      isDefault: false
    }
  ]);

  // Handle billing address form changes
  const handleBillingAddressChange = (field, value) => {
    setBillingAddress(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  // Handle billing address form submission
  const handleBillingAddressSubmit = async (e) => {
    e.preventDefault();

    setLoading(true);
    setError('');

    try {
      // Update billing address in the database
      await updateProfile({
        billing_address: billingAddress.address,
        billing_city: billingAddress.city,
        billing_state: billingAddress.state,
        billing_country: billingAddress.country,
        billing_postal_code: billingAddress.postalCode
      });

      setShowBillingAddress(false);
      // Show success message (you might want to add a toast notification)
      alert('Billing address updated successfully');
    } catch (err) {
      setError('Failed to update billing address');
    } finally {
      setLoading(false);
    }
  };

  // Generate billing history based on user's subscription
  const getBillingHistory = () => {
    const planName = getPlanName(profile?.subscription_tier);
    const planPrice = getPlanPrice(profile?.subscription_tier);
    
    // If user is on free plan, return empty history
    if (!profile?.subscription_tier || profile.subscription_tier === 'free') {
      return [];
    }

    // Generate recent billing entries based on subscription
    const currentDate = new Date();
    const history = [];
    
    // Add current month's invoice if subscription is active
    if (profile?.subscription_status === 'active') {
      history.push({
        id: `INV-${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(3, '0')}`,
        date: currentDate.toLocaleDateString('en-CA'), // YYYY-MM-DD format
        description: `${planName} - Monthly`,
        amount: planPrice,
        status: 'paid',
        downloadUrl: '#'
      });
    }

    // Add previous months (last 3 months)
    for (let i = 1; i <= 3; i++) {
      const pastDate = new Date(currentDate);
      pastDate.setMonth(pastDate.getMonth() - i);
      
      history.push({
        id: `INV-${pastDate.getFullYear()}-${String(pastDate.getMonth() + 1).padStart(3, '0')}`,
        date: pastDate.toLocaleDateString('en-CA'),
        description: `${planName} - Monthly`,
        amount: planPrice,
        status: 'paid',
        downloadUrl: '#'
      });
    }

    return history;
  };

  const [billingHistory] = useState(getBillingHistory());

  // Calculate current billing info dynamically
  const getCurrentBilling = () => ({
    plan: getPlanName(profile?.subscription_tier),
    amount: getPlanPrice(profile?.subscription_tier),
    nextBilling: profile?.subscription_expires_at ?
      new Date(profile.subscription_expires_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }) : 'N/A',
    paymentMethod: paymentMethods.find(pm => pm.isDefault)?.type === 'card'
      ? `${paymentMethods.find(pm => pm.isDefault)?.brand?.toUpperCase()} ending in ${paymentMethods.find(pm => pm.isDefault)?.last4}`
      : 'No payment method',
    billingAddress: {
      name: billingAddress.name || profile?.full_name || 'N/A',
      address: billingAddress.address || 'No address provided',
      city: billingAddress.city || 'N/A',
      state: billingAddress.state || 'N/A',
      country: billingAddress.country || 'N/A',
      postalCode: billingAddress.postalCode || 'N/A'
    }
  });

  const currentBilling = getCurrentBilling();

  const handleAddPaymentMethod = (method) => {
    prodLogger.debug('Adding payment method:', method);
    setShowAddPaymentMethod(false);
  };

  const handleSetDefaultPayment = (methodId) => {
    prodLogger.debug('Setting default payment method:', methodId);
  };

  const handleRemovePaymentMethod = (methodId) => {
    prodLogger.debug('Removing payment method:', methodId);
  };

  const handleDownloadInvoice = (invoice) => {
    prodLogger.debug('Downloading invoice:', invoice.id);
  };

  const handleViewInvoice = (invoice) => {
    setSelectedInvoice(invoice);
    setShowInvoiceModal(true);
  };

  const getPaymentMethodIcon = (type, brand) => {
    if (type === 'card') {
      switch (brand) {
        case 'visa': return 'CreditCard';
        case 'mastercard': return 'CreditCard';
        default: return 'CreditCard';
      }
    }
    return 'Building2';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'text-success bg-success/10';
      case 'failed': return 'text-error bg-error/10';
      case 'pending': return 'text-warning bg-warning/10';
      default: return 'text-text-secondary bg-background';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Billing */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Current Billing</h3>
          <p className="text-sm text-text-secondary">Your current subscription and billing information</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">Current Plan</span>
                <span className="text-lg font-semibold text-primary">{currentBilling.plan}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-text-secondary">
                <span>Next billing date</span>
                <span>{currentBilling.nextBilling}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-text-secondary">
                <span>Amount</span>
                <span className="font-medium">{currentBilling.amount}</span>
              </div>
            </div>

            <div className="p-4 border border-border rounded-lg">
              <h4 className="text-sm font-medium text-text-primary mb-2">Payment Method</h4>
              <p className="text-sm text-text-secondary">{currentBilling.paymentMethod}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-text-primary">Billing Address</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBillingAddress(true)}
                  iconName="Edit"
                >
                  Edit
                </Button>
              </div>
              <div className="space-y-1 text-sm text-text-secondary">
                <p>{currentBilling.billingAddress.name}</p>
                <p>{currentBilling.billingAddress.address}</p>
                <p>{currentBilling.billingAddress.city}, {currentBilling.billingAddress.state}</p>
                <p>{currentBilling.billingAddress.country} {currentBilling.billingAddress.postalCode}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Methods */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          {/* Desktop Layout - Side by side */}
          <div className="hidden sm:flex sm:items-start sm:justify-between gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-text-primary">Payment Methods</h3>
            </div>
            <div className="flex-shrink-0">
              <Button
                variant="outline"
                onClick={() => setShowAddPaymentMethod(true)}
                iconName="Plus"
                iconPosition="left"
                size="sm"
                className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
              >
                Add Payment Method
              </Button>
            </div>
          </div>

          {/* Mobile Layout - Stacked */}
          <div className="sm:hidden mb-2">
            <h3 className="text-lg font-semibold text-text-primary mb-3">Payment Methods</h3>
            <Button
              variant="outline"
              onClick={() => setShowAddPaymentMethod(true)}
              iconName="Plus"
              iconPosition="left"
              size="sm"
              className="w-full text-sm py-2 px-4"
            >
              Add Payment Method
            </Button>
          </div>

          <p className="text-sm text-text-secondary">Manage your saved payment methods</p>
        </div>

        <div className="space-y-3">
          {paymentMethods.map((method) => (
            <div key={method.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3 min-w-0 flex-1">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Icon name={getPaymentMethodIcon(method.type, method.brand)} size={16} color="var(--color-secondary)" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm font-medium text-text-primary break-words">
                      {method.type === 'card'
                        ? `${method.brand.toUpperCase()} ending in ${method.last4}`
                        : `${method.bankName} ${method.accountNumber}`
                      }
                    </span>
                    {method.isDefault && (
                      <span className="text-xs bg-primary text-primary-foreground px-2 py-0.5 rounded-full flex-shrink-0">
                        Default
                      </span>
                    )}
                  </div>
                  {method.type === 'card' && (
                    <p className="text-xs text-text-secondary">
                      Expires {method.expiryMonth}/{method.expiryYear}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2 flex-shrink-0">
                {!method.isDefault && (
                  <Button
                    variant="ghost"
                    onClick={() => handleSetDefaultPayment(method.id)}
                    size="sm"
                    className="whitespace-nowrap"
                  >
                    <Icon name="Star" size={14} className="sm:hidden" />
                    <span className="hidden sm:inline">Set Default</span>
                  </Button>
                )}
                <Button
                  variant="ghost"
                  onClick={() => handleRemovePaymentMethod(method.id)}
                  size="sm"
                  className="text-error hover:text-error hover:bg-error/10"
                >
                  <Icon name="Trash2" size={14} />
                  <span className="hidden sm:inline ml-1">Delete</span>
                </Button>
              </div>
            </div>
          ))}
        </div>

        {/* Add Payment Method Modal */}
        {showAddPaymentMethod && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
            <div className="bg-surface rounded-lg border border-border max-w-md w-full">
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-text-primary">Add Payment Method</h4>
                  <Button variant="ghost" onClick={() => setShowAddPaymentMethod(false)}>
                    <Icon name="X" size={20} />
                  </Button>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <div className="grid grid-cols-2 gap-2">
                  <button className="p-4 border border-border rounded-lg hover:border-primary transition-micro text-center">
                    <Icon name="CreditCard" size={24} className="mx-auto mb-2" />
                    <span className="text-sm text-text-primary">Card</span>
                  </button>
                  <button className="p-4 border border-border rounded-lg hover:border-primary transition-micro text-center">
                    <Icon name="Building2" size={24} className="mx-auto mb-2" />
                    <span className="text-sm text-text-primary">Bank Account</span>
                  </button>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">Card Number</label>
                    <Input type="text" placeholder="1234 5678 9012 3456" />
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">Expiry Date</label>
                      <Input type="text" placeholder="MM/YY" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-text-primary mb-2">CVV</label>
                      <Input type="text" placeholder="123" />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text-primary mb-2">Cardholder Name</label>
                    <Input type="text" placeholder="John Doe" />
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="primary" onClick={() => handleAddPaymentMethod({})} className="flex-1">
                    Add Payment Method
                  </Button>
                  <Button variant="ghost" onClick={() => setShowAddPaymentMethod(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Billing History */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Billing History</h3>
          <p className="text-sm text-text-secondary">Download your invoices and view payment history</p>
        </div>

        <div className="space-y-3">
          {billingHistory.map((invoice) => (
            <div key={invoice.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                  <Icon name="Receipt" size={16} color="var(--color-secondary)" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">{invoice.id}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </span>
                  </div>
                  <p className="text-xs text-text-secondary">
                    {invoice.date} • {invoice.description}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-text-primary">{invoice.amount}</span>
                <div className="flex items-center space-x-1">
                  <Button variant="ghost" onClick={() => handleViewInvoice(invoice)}>
                    <Icon name="Eye" size={16} />
                  </Button>
                  {invoice.downloadUrl && (
                    <Button variant="ghost" onClick={() => handleDownloadInvoice(invoice)}>
                      <Icon name="Download" size={16} />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Invoice Modal */}
      {showInvoiceModal && selectedInvoice && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
          <div className="bg-surface rounded-lg border border-border max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <h4 className="text-lg font-semibold text-text-primary">Invoice {selectedInvoice.id}</h4>
                <Button variant="ghost" onClick={() => setShowInvoiceModal(false)}>
                  <Icon name="X" size={20} />
                </Button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex justify-between">
                  <div>
                    <h5 className="font-semibold text-text-primary mb-2">RapidDoc AI</h5>
                    <p className="text-sm text-text-secondary">
                      123 Business District<br />
                      Victoria Island, Lagos<br />
                      Nigeria
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-text-secondary">Invoice Date: {selectedInvoice.date}</p>
                    <p className="text-sm text-text-secondary">Due Date: {selectedInvoice.date}</p>
                  </div>
                </div>

                <div>
                  <h6 className="font-medium text-text-primary mb-2">Bill To:</h6>
                  <p className="text-sm text-text-secondary">
                    {currentBilling.billingAddress.name}<br />
                    {currentBilling.billingAddress.address}<br />
                    {currentBilling.billingAddress.city}, {currentBilling.billingAddress.state}<br />
                    {currentBilling.billingAddress.country} {currentBilling.billingAddress.postalCode}
                  </p>
                </div>

                <div className="border border-border rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-background">
                      <tr>
                        <th className="text-left p-3 text-sm font-medium text-text-primary">Description</th>
                        <th className="text-right p-3 text-sm font-medium text-text-primary">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="p-3 text-sm text-text-secondary">{selectedInvoice.description}</td>
                        <td className="p-3 text-sm text-text-primary text-right">{selectedInvoice.amount}</td>
                      </tr>
                    </tbody>
                    <tfoot className="bg-background">
                      <tr>
                        <td className="p-3 text-sm font-medium text-text-primary">Total</td>
                        <td className="p-3 text-sm font-medium text-text-primary text-right">{selectedInvoice.amount}</td>
                      </tr>
                    </tfoot>
                  </table>
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => handleDownloadInvoice(selectedInvoice)}>
                    Download PDF
                  </Button>
                  <Button variant="primary" onClick={() => setShowInvoiceModal(false)}>
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Billing Address Edit Modal */}
      {showBillingAddress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-surface rounded-lg border border-border p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-text-primary">Edit Billing Address</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBillingAddress(false)}
                iconName="X"
              />
            </div>

            {error && (
              <div className="bg-error/10 border border-error/20 rounded-lg p-3 mb-4">
                <div className="flex items-center space-x-2">
                  <Icon name="AlertCircle" size={14} color="var(--color-error)" />
                  <span className="text-sm text-error">{error}</span>
                </div>
              </div>
            )}

            <form onSubmit={handleBillingAddressSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Full Name</label>
                <Input
                  type="text"
                  value={billingAddress.name}
                  onChange={(e) => handleBillingAddressChange('name', e.target.value)}
                  placeholder="Enter full name"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Address</label>
                <Input
                  type="text"
                  value={billingAddress.address}
                  onChange={(e) => handleBillingAddressChange('address', e.target.value)}
                  placeholder="Enter street address"
                  required
                  disabled={loading}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">City</label>
                  <Input
                    type="text"
                    value={billingAddress.city}
                    onChange={(e) => handleBillingAddressChange('city', e.target.value)}
                    placeholder="Enter city"
                    required
                    disabled={loading}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">State</label>
                  <Input
                    type="text"
                    value={billingAddress.state}
                    onChange={(e) => handleBillingAddressChange('state', e.target.value)}
                    placeholder="Enter state"
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">Country</label>
                  <Input
                    type="text"
                    value={billingAddress.country}
                    onChange={(e) => handleBillingAddressChange('country', e.target.value)}
                    placeholder="Enter country"
                    required
                    disabled={loading}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">Postal Code</label>
                  <Input
                    type="text"
                    value={billingAddress.postalCode}
                    onChange={(e) => handleBillingAddressChange('postalCode', e.target.value)}
                    placeholder="Enter postal code"
                    required
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="flex space-x-2 pt-4">
                <Button
                  type="submit"
                  variant="primary"
                  loading={loading}
                  disabled={loading}
                  className="flex-1"
                >
                  Save Address
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setShowBillingAddress(false)}
                  disabled={loading}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default BillingSection;