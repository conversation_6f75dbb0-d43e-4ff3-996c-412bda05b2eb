import React, { useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useProjects } from '../../../hooks/useProjects';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

import { prodLogger } from '../../../utils/prodLogger.js';
const SubscriptionSection = () => {
  const { user, profile, loading: authLoading } = useAuth();
  
  // Fetch all user projects for statistics
  const { projects, isLoading: projectsLoading } = useProjects({
    sortBy: 'updated_at',
    sortOrder: 'desc',
    limit: null, // Get all projects for stats
    includeContent: false
  });
  
  // Map subscription tiers to plan details
  const getPlanDetails = (subscriptionTier) => {
    const planMap = {
      'free': {
        name: "Free",
        price: "₦0",
        period: "monthly",
        credits: 50,
        features: [
          "Basic document creation",
          "Standard templates", 
          "PDF export",
          "Email support"
        ]
      },
      'pro': {
        name: "Professional", 
        price: "₦15,000",
        period: "monthly",
        credits: 1000,
        features: [
          "Unlimited document creation",
          "Advanced AI features",
          "Priority support", 
          "Export to all formats"
        ]
      },
      'enterprise': {
        name: "Enterprise",
        price: "₦35,000", 
        period: "monthly",
        credits: 5000,
        features: [
          "Everything in Professional",
          "Custom branding",
          "API access",
          "Dedicated support",
          "Advanced analytics"
        ]
      }
    };
    
    return planMap[subscriptionTier] || planMap['free'];
  };

  // Get current plan from profile
  const currentPlan = {
    ...getPlanDetails(profile?.subscription_tier || 'free'),
    usedCredits: profile?.ai_generations_used || 0,
    nextBilling: profile?.subscription_expires_at ? 
      new Date(profile.subscription_expires_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit', 
        day: '2-digit'
      }) : "N/A"
  };

  // Calculate real-time usage statistics
  const getUsageStats = () => {
    if (authLoading || projectsLoading) {
      return [
        { label: "Documents Created", value: "—", icon: "FileText", isLoading: true },
        { label: "Projects Active", value: "—", icon: "Library", isLoading: true },
      ];
    }

    // Calculate actual statistics
    const documentsCreated = profile?.documents_created || projects.length || 0;
    const activeProjects = projects.filter(project =>
      project.status === 'draft' || project.status === 'in_progress'
    ).length || 0;

    return [
      { label: "Documents Created", value: documentsCreated, icon: "FileText", isLoading: false },
      { label: "Projects Active", value: activeProjects, icon: "Library", isLoading: false }
    ];
  };

  const usageStats = getUsageStats();

  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const plans = [
    {
      name: "Basic",
      price: "₦5,000",
      period: "monthly",
      credits: 200,
      features: [
        "Basic document creation",
        "Standard templates",
        "PDF export",
        "Email support"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: "₦15,000",
      period: "monthly",
      credits: 1000,
      features: [
        "Unlimited document creation",
        "Advanced AI features",
        "Priority support",
        "Export to all formats"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "₦35,000",
      period: "monthly",
      credits: 5000,
      features: [
        "Everything in Professional",
        "Custom branding",
        "API access",
        "Dedicated support",
        "Advanced analytics"
      ],
      popular: false
    }
  ];

  const creditPercentage = currentPlan.credits > 0 ? 
    (currentPlan.usedCredits / currentPlan.credits) * 100 : 0;

  const handleUpgrade = (planName) => {
    prodLogger.debug('Upgrading to:', planName);
    setShowUpgradeModal(false);
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <div className="flex items-start justify-between gap-3 mb-2">
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-text-primary">Current Subscription</h2>
            </div>
            <div className="flex-shrink-0">
              <Button
                variant="outline"
                onClick={() => setShowUpgradeModal(true)}
                iconName="ArrowUp"
                iconPosition="left"
                size="sm"
                className="whitespace-nowrap text-sm lg:text-base lg:py-2 lg:px-4"
              >
                <span className="hidden sm:inline">Upgrade Plan</span>
                <span className="sm:hidden">Upgrade</span>
              </Button>
            </div>
          </div>
          <p className="text-sm text-text-secondary">Manage your plan and billing</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Plan Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="Crown" size={24} color="var(--color-primary)" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text-primary">{currentPlan.name} Plan</h3>
                <p className="text-sm text-text-secondary">
                  {currentPlan.price}/{currentPlan.period} • Next billing: {currentPlan.nextBilling}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              {currentPlan.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Icon name="Check" size={16} color="var(--color-success)" />
                  <span className="text-sm text-text-secondary">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Credit Usage */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">Credit Usage</span>
                <span className="text-sm text-text-secondary">
                  {currentPlan.usedCredits} / {currentPlan.credits} used
                </span>
              </div>
              <div className="w-full bg-background rounded-full h-3">
                <div
                  className="bg-primary h-3 rounded-full transition-all duration-300"
                  style={{ width: `${creditPercentage}%` }}
                ></div>
              </div>
              <p className="text-xs text-text-secondary mt-1">
                {currentPlan.credits - currentPlan.usedCredits} credits remaining
              </p>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {usageStats.map((stat, index) => (
                <div key={index} className="bg-background p-4 sm:p-3 rounded-lg">
                  <div className="flex items-center mb-2 sm:mb-1">
                    <Icon
                      name={stat.icon}
                      size={16}
                      className="hidden sm:block sm:mr-2"
                      color="var(--color-secondary)"
                    />
                    <span className="text-sm sm:text-xs text-text-secondary font-medium">{stat.label}</span>
                  </div>
                  <p className={`text-xl sm:text-lg font-semibold ${
                    stat.isLoading
                      ? 'text-text-secondary animate-pulse'
                      : 'text-text-primary'
                  }`}>
                    {stat.value}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
          <div className="bg-surface rounded-lg border border-border max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-text-primary">Choose Your Plan</h3>
                  <p className="text-sm text-text-secondary">Select the plan that best fits your needs</p>
                </div>
                <Button variant="ghost" onClick={() => setShowUpgradeModal(false)}>
                  <Icon name="X" size={20} />
                </Button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.map((plan, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-6 relative ${
                      plan.popular ? 'border-primary bg-primary/5' : 'border-border'
                    }`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-6">
                      <h4 className="text-xl font-semibold text-text-primary mb-2">{plan.name}</h4>
                      <div className="mb-4">
                        <span className="text-3xl font-bold text-primary">{plan.price}</span>
                        <span className="text-text-secondary">/{plan.period}</span>
                      </div>
                      <p className="text-sm text-text-secondary">{plan.credits} credits included</p>
                    </div>

                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <Icon name="Check" size={16} color="var(--color-success)" />
                          <span className="text-sm text-text-secondary">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Button
                      variant={plan.popular ? "primary" : "outline"}
                      onClick={() => handleUpgrade(plan.name)}
                      className="w-full"
                    >
                      {currentPlan.name === plan.name ? 'Current Plan' : 'Upgrade'}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionSection;