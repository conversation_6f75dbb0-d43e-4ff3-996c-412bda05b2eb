import React from 'react';

/**
 * TemplateLoadingScreen - Shows loading state during template-related operations
 * Displays appropriate messaging for template loading, fetching, and processing
 */
const TemplateLoadingScreen = ({ 
  message = "Loading templates...", 
  subtitle = null,
  showSteps = false 
}) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center px-6">
        {/* Logo/Icon */}
        <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-8">
          <svg className="w-10 h-10 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>

        {/* Title */}
        <h1 className="text-2xl md:text-3xl font-bold text-text-primary mb-2">
          {message}
        </h1>
        
        {subtitle && (
          <h2 className="text-lg text-text-secondary mb-8">
            {subtitle}
          </h2>
        )}

        {/* Loading Animation */}
        <div className="mb-8">
          {/* Animated dots */}
          <div className="flex justify-center space-x-1">
            <div className="w-3 h-3 bg-primary rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
            <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          </div>
        </div>

        {/* Optional Steps List for more complex template operations */}
        {showSteps && (
          <div className="text-left bg-white/50 rounded-lg p-6 backdrop-blur-sm">
            <h3 className="font-semibold text-text-primary mb-4">Loading Steps:</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-primary">
                <div className="w-5 h-5 rounded-full flex items-center justify-center bg-primary/10">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                </div>
                <span className="text-sm">Fetching document data</span>
              </div>
              
              <div className="flex items-center space-x-3 text-gray-400">
                <div className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100">
                  <span className="text-xs">2</span>
                </div>
                <span className="text-sm">Loading available templates</span>
              </div>
              
              <div className="flex items-center space-x-3 text-gray-400">
                <div className="w-5 h-5 rounded-full flex items-center justify-center bg-gray-100">
                  <span className="text-xs">3</span>
                </div>
                <span className="text-sm">Preparing template selection</span>
              </div>
            </div>
          </div>
        )}

        {/* Tip */}
        <div className="mt-6 text-sm text-text-secondary">
          💡 <strong>Tip:</strong> Your templates will be ready for selection in just a moment!
        </div>
      </div>
    </div>
  );
};

export default TemplateLoadingScreen;
