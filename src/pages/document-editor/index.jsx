import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import DocumentCanvasMinimal from './components/DocumentCanvasMinimal';
import DocumentWorkflowHeader from './components/DocumentWorkflowHeader';
import DocumentInfoHeader from './components/DocumentInfoHeader';
import DocumentSidebar from './components/DocumentSidebar';
import LoadingScreen from './components/LoadingScreen';
import ImageSelectionModal from './components/ImageSelectionModal';
import ExportSuccessModal from './components/ExportSuccessModal';
import { generateDocumentContent } from '../../services/aiService';
import useReviewMode from './hooks/useReviewMode';
import { generateImageSuggestions } from '../../services/unsplashService';
import { exportDocument } from '../../services/exportService';
// Image migration removed - using simple read-only document display
// Removed documentDataDiagnostic - utility was deleted during cleanup
import '../../utils/cacheManager'; // Load cache management tools
import { useErrorMonitor } from '../../utils/useErrorMonitor';
import { handlePhaseTransition } from '../../utils/progressUtils';
import { documentStorage } from '../../services/documentStorageService';
import { useUserActivity } from '../../hooks/useUserActivity';

import { prodLogger } from '../../utils/prodLogger.js';
/**
 * DocumentEditor - Main document editing interface
 * Canvas-style editor similar to Designrr with AI content generation
 */
const DocumentEditor = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { contentMargin } = useSidebar();
  
  // Initialize error monitoring for this component
  const logger = useErrorMonitor('DocumentEditor', { documentId });

  // Initialize user activity tracking
  const { trackSkipTemplateUsage, trackTemplateSelectionUsage } = useUserActivity();

  // Utility function to get user's template preference
  const getUserTemplatePreference = () => {
    try {
      const preference = localStorage.getItem('docforge_skip_template_preference');
      const skipLastUsed = localStorage.getItem('docforge_skip_template_last_used');
      const templateLastUsed = localStorage.getItem('docforge_template_selection_last_used');

      return {
        prefersSkipTemplate: preference === 'true',
        skipLastUsed: skipLastUsed ? new Date(skipLastUsed) : null,
        templateLastUsed: templateLastUsed ? new Date(templateLastUsed) : null,
        hasPreference: preference !== null
      };
    } catch (error) {
      prodLogger.warn('⚠️ Failed to read template preference:', error);
      return {
        prefersSkipTemplate: false,
        skipLastUsed: null,
        templateLastUsed: null,
        hasPreference: false
      };
    }
  };

  const [documentData, setDocumentData] = useState(null);
  const [generatedContent, setGeneratedContent] = useState(null);
  const [imageSuggestions, setImageSuggestions] = useState({});
  const [isGenerating, setIsGenerating] = useState(false); // Start as false for existing documents
  const [userTemplatePreference, setUserTemplatePreference] = useState(null);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');

  // Image selection modal state
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedChapterId, setSelectedChapterId] = useState(null);
  const [error, setError] = useState(null);
  const [saveStatus, setSaveStatus] = useState('saved'); // 'saving' | 'saved' | 'error'
  const [lastSaved, setLastSaved] = useState(null);

  // TipTap editor instance reference for export functionality
  const [editorInstance, setEditorInstance] = useState(null);

  // Export modal state
  const [showExportModal, setShowExportModal] = useState(false);
  const [exportResult, setExportResult] = useState(null);
  const [exportError, setExportError] = useState(null);
  const [exportFormat, setExportFormat] = useState('pdf');

  // Review Phase State Management using custom hook
  const {
    currentPhase,
    reviewData,
    isReviewMode,
    isEditMode,
    reviewCompletionPercentage,
    isReviewReadyForCompletion,
    enterReviewMode,
    exitReviewMode,
    updateReviewData,
    setValidationResults,

    setExportReadiness,
    completeReview
  } = useReviewMode('Edit Content');

  // Using simple read-only document display

  // Load document data from database
  useEffect(() => {
    const loadDocumentData = async () => {
      try {
        logger.info('Loading document data from database', { documentId });

        // Load document from database
        const result = await documentStorage.loadDocument(documentId);

        if (result.success) {
          const data = result.data;

          logger.info('Document data loaded successfully', {
            documentId,
            source: result.source,
            hasGeneratedContent: !!data.generated_content,
            lastModified: data.updated_at
          });

          // Convert database format to component format
          const documentData = {
            ...data.questionnaire_data,
            generatedContent: data.generated_content,
            lastModified: data.updated_at,
            createdAt: data.created_at,
            documentId: documentId,
            projectId: documentId
          };

          setDocumentData(documentData);
          return documentData;
        } else {
          // If document not found, redirect back to document creator
          logger.warn('Document not found, redirecting to creator', {
            documentId,
            error: result.error
          });
          navigate('/document-creator');
          return null;
        }
      } catch (error) {
        logger.error(error, {
          action: 'load_document_data',
          documentId
        });
        setError('Failed to load document data');
        return null;
      }
    };

    loadDocumentData().then(data => {
      if (data) {
        // Check if document already has generated content (edit phase)
        if (data.generatedContent && Object.keys(data.generatedContent).length > 0) {
          logger.info('Document already has generated content, loading existing content', {
            documentId,
            hasContent: !!data.generatedContent,
            lastModified: data.lastModified
          });

          // Load existing content without regenerating
          // Don't set generatedContent immediately - let the loading state show briefly
          setTimeout(() => {
            setGeneratedContent(data.generatedContent);
            
            // Load existing image suggestions if available
            if (data.imageAddition?.suggestions) {
              setImageSuggestions(data.imageAddition.suggestions);
              logger.info('Loaded existing image suggestions', {
                suggestionCount: Object.keys(data.imageAddition.suggestions).length
              });
            }

            // Ensure we're not in generating state
            setIsGenerating(false);

            logger.info('Edit phase: Existing content loaded successfully', {
              documentId,
              wordCount: data.generatedContent.wordCount || 'unknown',
              chapterCount: data.generatedContent.chapters?.length || 0
            });
          }, 500); // Brief delay to show loading state with header and sidebar
        } else {
          // No generated content exists - this is initial generation from wizard
          logger.info('No generated content found, starting initial AI generation', {
            documentId,
            hasOutline: !!data.documentOutline?.generatedOutline
          });

          generateDocument(data).catch(error => {
            prodLogger.error('Error in generateDocument:', error);
            setError(error.message || 'Failed to generate document');
          });
        }
      }
    }).catch(error => {
      prodLogger.error('Error loading document data:', error);
      setError('Failed to load document');
    });

    // Load user's template preference
    const preference = getUserTemplatePreference();
    setUserTemplatePreference(preference);
    if (preference.hasPreference) {
      prodLogger.debug('📋 User template preference loaded:', {
        prefersSkipTemplate: preference.prefersSkipTemplate,
        lastUsed: preference.prefersSkipTemplate ? preference.skipLastUsed : preference.templateLastUsed
      });
    }
  }, [documentId, navigate]);

  // Review mode activation removed - now handled by dedicated route

  // Review mode handlers removed - now handled by dedicated route

  // Handle navigation to publish phase
  const handleNavigateToPublish = () => {
    navigate(`/document-editor/${documentId}/publish`);
  };

  // Generate document content using AI (only for initial generation)
  const generateDocument = async (data) => {
    logger.info('Starting initial document generation from wizard', {
      documentType: data.documentPurpose?.primaryType || 'unknown',
      hasOutline: !!data.documentOutline?.generatedOutline,
      hasImportedContent: !!data.documentPurpose?.importedContent?.extractedContent,
      documentId
    });
    
    try {
      setIsGenerating(true);
      setError(null);
      
      // Step 1: Generate content
      setCurrentStep('Generating document content...');
      setGenerationProgress(20);
      logger.debug('Generating document content');
      
      const contentResult = await generateDocumentContent(data);
      
      if (!contentResult.success) {
        logger.error(new Error('Failed to generate document content'), {
          step: 'content_generation',
          success: false
        });
        throw new Error('Failed to generate document content');
      }
      
      logger.info('Document content generated successfully', {
        wordCount: contentResult.content.wordCount,
        chapterCount: contentResult.content.chapters?.length || 0
      });
      
      setGeneratedContent(contentResult.content);
      setGenerationProgress(60);

      // Store processed image suggestions for localStorage save
      let processedImageSuggestions = {};

      // Step 2: Use image suggestions if enabled (auto-generated from DocumentOutlineStep)
      if (data.imageAddition?.enabled) {
        setCurrentStep('Loading image suggestions...');
        setGenerationProgress(80);
        logger.debug('Loading AI-generated image suggestions');

        try {
          // Check if suggestions were already generated in DocumentOutlineStep
          let imageSuggestions = data.imageAddition?.suggestions;

          if (!imageSuggestions || Object.keys(imageSuggestions).length === 0) {
            // Fallback: generate suggestions if not available (shouldn't happen with new workflow)
            logger.warn('Image suggestions not found, generating as fallback');
            imageSuggestions = await generateImageSuggestions(
              data,
              data.documentOutline?.generatedOutline
            );
          } else {
            logger.info('Using pre-generated image suggestions from outline step');
          }

          setImageSuggestions(imageSuggestions);
          processedImageSuggestions = imageSuggestions; // Store for localStorage save

          // DEBUG: Log image suggestions structure
          prodLogger.debug('🔍 DEBUG: Image suggestions set:', imageSuggestions);
          prodLogger.debug('🔍 DEBUG: Image suggestions keys:', Object.keys(imageSuggestions));
          prodLogger.debug('🔍 DEBUG: First chapter data:', imageSuggestions['chapter-1']);

          logger.info('Image suggestions generated', {
            suggestionCount: Object.keys(imageSuggestions).length
          });
        } catch (imageError) {
          // Non-fatal error - log but continue
          logger.warn('Failed to generate image suggestions', {
            error: imageError.message
          });
        }
      }
      
      // Step 3: Complete
      setCurrentStep('Finalizing document...');
      setGenerationProgress(100);
      logger.info('Document generation completed');

      // CRITICAL FIX: Save generated content to localStorage immediately after generation
      // This ensures that page reloads will detect existing content and not trigger regeneration
      try {
        const documentToSave = {
          ...data,
          generatedContent: contentResult.content,
          imageAddition: data.imageAddition?.enabled ? {
            ...data.imageAddition,
            suggestions: processedImageSuggestions
          } : data.imageAddition,
          lastModified: new Date().toISOString()
        };

        // Save to database immediately after generation
        const saveResult = await documentStorage.saveDocument(documentId, documentToSave, { immediate: true });

        if (saveResult.success) {
          logger.info('Generated content saved to database after initial AI generation', {
            documentId,
            wordCount: contentResult.content.wordCount || 'unknown',
            chapterCount: contentResult.content.chapters?.length || 0,
            hasImageSuggestions: !!(data.imageAddition?.enabled && processedImageSuggestions && Object.keys(processedImageSuggestions).length > 0)
          });
        } else {
          logger.warn('Failed to save generated content to database', {
            error: saveResult.error,
            documentId
          });
        }
      } catch (saveError) {
        // Non-fatal error - log but continue
        logger.warn('Failed to save generated content to localStorage', {
          error: saveError.message,
          documentId
        });
      }

      // Update document progress to reflect transition from Generate → Edit Content
      try {
        await handlePhaseTransition(documentId, 'Edit Content', 'Generate');
        logger.info('Document progress updated to Edit Content phase (50%)');
      } catch (progressError) {
        // Non-fatal error - log but continue
        logger.warn('Failed to update document progress', {
          error: progressError.message,
          documentId
        });
      }

      // Small delay for smooth UX
      setTimeout(() => {
        setIsGenerating(false);
      }, 1000);
      
    } catch (error) {
      logger.error(error, {
        step: 'document_generation',
        documentId: documentId
      });
      setError(error.message || 'Failed to generate document');
      setIsGenerating(false);
    }
  };

  // Handle manual save triggered by save button
  const handleManualSave = async () => {
    if (!documentData || !generatedContent) {
      logger.warn('Cannot save: missing document data or content');
      return;
    }

    try {
      setSaveStatus('saving');

      const documentToSave = {
        ...documentData,
        generatedContent: generatedContent,
        lastModified: new Date().toISOString()
      };

      // Use immediate save (bypasses debouncing for manual saves)
      const saveResult = await documentStorage.saveDocument(documentId, documentToSave, { immediate: true });

      if (saveResult.success) {
        setSaveStatus('saved');
        setLastSaved(new Date());
        logger.info('Document manually saved', {
          documentId,
          lastModified: documentToSave.lastModified,
          hasGeneratedContent: !!documentToSave.generatedContent
        });

        // Brief success feedback - keep saved state for user confirmation
        // The auto-save system will handle subsequent changes
      } else {
        setSaveStatus('error');
        logger.error('Manual save failed', {
          action: 'manual_save',
          documentId,
          error: saveResult.error
        });

        // Auto-clear error state after delay to allow retry
        setTimeout(() => {
          setSaveStatus('saved'); // Reset to allow retry
        }, 3000);
      }
    } catch (error) {
      setSaveStatus('error');
      logger.error('Manual save error', {
        action: 'manual_save',
        documentId,
        error: error.message
      });

      // Auto-clear error state after delay to allow retry
      setTimeout(() => {
        setSaveStatus('saved'); // Reset to allow retry
      }, 3000);
    }
  };

  // Handle content updates from the editor
  const handleContentUpdate = async (updatedContent) => {
    // Check if updatedContent is HTML string (from editor) or structured object
    if (typeof updatedContent === 'string') {
      // HTML content from editor - update the existing generatedContent structure
      const updatedGeneratedContent = {
        ...generatedContent,
        // Store the raw HTML for potential future use
        editorHTML: updatedContent,
        lastModified: new Date().toISOString()
      };

      setGeneratedContent(updatedGeneratedContent);

      logger.debug('Editor HTML content updated', {
        htmlLength: updatedContent.length,
        documentId
      });

      // Auto-save to database with updated HTML
      setSaveStatus('saving');
      const documentToSave = {
        ...documentData,
        generatedContent: updatedGeneratedContent,
        lastModified: new Date().toISOString()
      };

      // Use debounced auto-save to database
      const saveResult = await documentStorage.saveDocument(documentId, documentToSave);

      if (saveResult.success) {
        setSaveStatus('saved');
        setLastSaved(new Date());
        logger.info('Document auto-saved with editor changes', {
          documentId,
          htmlLength: updatedContent.length,
          lastModified: documentToSave.lastModified,
          hasGeneratedContent: !!documentToSave.generatedContent,
          scheduled: saveResult.scheduled
        });
      } else {
        setSaveStatus('error');
        logger.error('Auto-save failed', {
          action: 'auto_save_editor_content',
          documentId,
          error: saveResult.error
        });
      }

      return;
    }

    // Handle structured content updates (legacy path)
    setGeneratedContent(updatedContent);

    // Enhanced debugging for content updates
    const chaptersWithImages = updatedContent.chapters ?
      updatedContent.chapters.filter(ch => ch.content && ch.content.includes('![')).length : 0;

    const contentMetrics = {
      title: updatedContent.title,
      hasLegacyPlacedImages: !!updatedContent.placedImages,
      legacyPlacedImagesCount: updatedContent.placedImages ?
        Object.keys(updatedContent.placedImages).reduce((total, key) =>
          total + updatedContent.placedImages[key].length, 0
        ) : 0,
      chaptersWithBlockImages: chaptersWithImages,
      totalChapters: updatedContent.chapters ? updatedContent.chapters.length : 0
    };

    logger.debug('Structured content updated and auto-saving', contentMetrics);

    // Warn if legacy placedImages are still present
    if (updatedContent.placedImages) {
      logger.warn('Legacy placedImages detected in content update', {
        issue: 'legacy_images_present',
        documentId
      });
    }

    // Auto-save to database
    const documentToSave = {
      ...documentData,
      generatedContent: updatedContent,
      lastModified: new Date().toISOString()
    };

    try {
      const saveResult = await documentStorage.saveDocument(documentId, documentToSave);

      if (saveResult.success) {
        logger.info('Document auto-saved successfully', {
          documentId,
          hasPlacedImages: !!updatedContent.placedImages,
          wordCount: updatedContent.wordCount || 'unknown',
          scheduled: saveResult.scheduled
        });
      } else {
        logger.warn('Auto-save failed', {
          documentId,
          error: saveResult.error
        });
      }
    } catch (error) {
      logger.error(error, {
        action: 'auto_save',
        documentId,
        storageKey: `document-${documentId}`
      });
    }
  };

  // Image selection modal handlers
  const handleOpenImageModal = (chapterId) => {
    setSelectedChapterId(chapterId);
    setIsImageModalOpen(true);
    logger.info('Opening image selection modal for chapter:', chapterId);
  };

  const handleCloseImageModal = () => {
    setIsImageModalOpen(false);
    setSelectedChapterId(null);
  };

  const handleImageSelect = (imageData) => {
    logger.info('Image selected:', imageData);
    // TODO: Implement image insertion into document content
    // This would integrate with the Tiptap editor to insert the selected image
    prodLogger.debug('Selected image for chapter:', selectedChapterId, imageData);
    handleCloseImageModal();
  };

  // Handle editor ready callback to store editor instance for export
  const handleEditorReady = (editor) => {
    setEditorInstance(editor);
  };

  // Handle template selection navigation
  const handleChooseTemplate = async () => {
    prodLogger.debug('🎨 Navigating to template selection');

    // Store user preference for template selection
    try {
      localStorage.setItem('docforge_skip_template_preference', 'false');
      localStorage.setItem('docforge_template_selection_last_used', new Date().toISOString());
      prodLogger.debug('💾 Template selection preference saved to localStorage');
    } catch (error) {
      prodLogger.warn('⚠️ Failed to save template selection preference:', error);
    }

    // Track template selection usage for analytics
    try {
      await trackTemplateSelectionUsage();
    } catch (error) {
      prodLogger.warn('⚠️ Failed to track template selection usage:', error);
    }

    navigate(`/document-template/${documentId}`);
  };

  // Handle export button click - redirect to template selection
  const handleExport = (format) => {
    prodLogger.debug('🚀 handleExport called - redirecting to template selection');
    prodLogger.debug('📊 State check:', {
      hasDocumentData: !!documentData,
      hasGeneratedContent: !!generatedContent,
      hasEditorInstance: !!editorInstance
    });

    if (!documentData || !generatedContent) {
      prodLogger.error('❌ Missing document data or generated content');
      setExportError('Document data not available for export');
      setExportResult(null);
      setShowExportModal(true);
      return;
    }

    // Redirect to template selection page
    navigate(`/document-template/${documentId}`);
  };

  // Handle review button click
  const handleReviewClick = () => {
    handlePhaseNavigation('Review');
  };

  // Handle export modal close
  const handleExportModalClose = () => {
    setShowExportModal(false);
    setExportResult(null);
    setExportError(null);
  };



  // Handle phase navigation in workflow header
  const handlePhaseNavigation = async (phase) => {
    prodLogger.debug(`Navigating to phase: ${phase}`);

    switch (phase) {
      case 'Generate':
        // Clear any cached AI-generated content before navigating back to wizard
        prodLogger.debug('Clearing cached AI content before returning to Generate phase');

        // Clear cached AI-generated content from localStorage
        const aiContentKeys = [
          'titleSelection.generatedTitles',
          'topicAndNiche.availableSubNiches',
          'documentOutline.generatedOutline'
        ];

        aiContentKeys.forEach(key => {
          const storageKey = `rapiddoc_cached_${key}`;
          localStorage.removeItem(storageKey);
        });

        // Navigate back to document creator with reset flag
        navigate('/document-creator', {
          state: { resetAIContent: true }
        });
        break;
      case 'Edit Content':
        // Already on edit content phase
        prodLogger.debug('Already on Edit Content phase');
        break;
      case 'Review':
        // OPTIMISTIC NAVIGATION: Navigate immediately for better UX
        prodLogger.debug('🚀 Optimistic Review navigation - processing operations in parallel');

        // Navigate immediately with current content for instant feedback
        navigate(`/document-editor/${documentId}/review`);

        // Run critical operations in parallel (non-blocking)
        Promise.allSettled([
          // Operation 1: Ensure document is saved
          (async () => {
            try {
              await documentStorage.ensureDocumentSaved(documentId);
              
              if (documentData && generatedContent) {
                const documentToSave = {
                  ...documentData,
                  generatedContent: generatedContent,
                  lastModified: new Date().toISOString()
                };
                
                const saveResult = await documentStorage.saveDocument(documentId, documentToSave, { immediate: true });
                if (saveResult.success) {
                  prodLogger.debug('✅ Document saved successfully in background');
                } else {
                  prodLogger.warn('⚠️ Background save had issues:', saveResult.error);
                }
              }
            } catch (error) {
              prodLogger.warn('⚠️ Background save error:', error);
            }
          })(),

          // Operation 2: Update progress
          (async () => {
            try {
              await handlePhaseTransition(documentId, 'Review', 'Edit Content');
            } catch (error) {
              prodLogger.warn('⚠️ Background progress update error:', error);
            }
          })()
        ]).then((results) => {
          const saveSuccess = results[0].status === 'fulfilled';
          const progressSuccess = results[1].status === 'fulfilled';
          
          prodLogger.debug('🎯 Review navigation background operations completed', {
            saveSuccess,
            progressSuccess,
            allSuccessful: saveSuccess && progressSuccess
          });
        });
        break;
      case 'template':
        // Navigate to template selection page
        prodLogger.debug('Navigating to template selection');
        navigate(`/document-template/${documentId}`);
        break;
      case 'Publish':
        // Update progress before navigating to publish phase
        try {
          await handlePhaseTransition(documentId, 'Publish', 'Review');
          prodLogger.debug('Progress updated for Publish phase');

          // Ensure document is saved before transitioning to publish
          if (documentData && generatedContent) {
            const documentToSave = {
              ...documentData,
              generatedContent: generatedContent,
              lastModified: new Date().toISOString()
            };
            await documentStorage.saveDocument(documentId, documentToSave, { immediate: true });
          }

          navigate(`/document-editor/${documentId}/publish`);
        } catch (error) {
          prodLogger.warn('Failed to update progress for Publish phase:', error);
          // Still navigate even if progress update fails
          navigate(`/document-editor/${documentId}/publish`);
        }
        break;
      default:
        prodLogger.warn(`Unknown phase: ${phase}`);
    }
  };

  // Show generation loading screen for new documents
  if (isGenerating) {
    return (
      <LoadingScreen 
        progress={generationProgress}
        currentStep={currentStep}
        documentTitle={documentData?.titleSelection?.selectedTitle || 'Your Document'}
      />
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center">
          <div className="border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <h3 className="font-bold">Error</h3>
            <p>{error}</p>
          </div>
          <button
            onClick={() => navigate('/document-creator')}
            className="bg-primary text-white px-6 py-2 rounded hover:bg-primary/90"
          >
            Back to Document Creator
          </button>
        </div>
      </div>
    );
  }

  if (!generatedContent) {
    return (
      <div className="min-h-screen bg-background">
        <QuickActionSidebar />
        <main className={`${contentMargin} ml-0 pt-16`}>
          {/* Document Workflow Header */}
          <DocumentWorkflowHeader
            currentPhase={currentPhase}
            onPhaseClick={handlePhaseNavigation}
          />

          {/* Document Info Header */}
          <DocumentInfoHeader
            documentTitle="Loading..."
            documentData={documentData}
            generatedContent={null}
            currentPhase={currentPhase}
            onReviewClick={handleReviewClick}
            onExportClick={handleExport}
            saveStatus={saveStatus}
            lastSaved={lastSaved}
            onChooseTemplate={handleChooseTemplate}
            showChooseTemplate={false} // Don't show during loading
          />

          {/* Loading Content */}
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading document...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <QuickActionSidebar />

      <main className={`${contentMargin} ml-0 pt-16 sidebar-layout`}>
        {/* Document Workflow Header */}
        <DocumentWorkflowHeader
          currentPhase={currentPhase}
          onPhaseClick={handlePhaseNavigation}
        />

        {/* Document Info Header */}
        <DocumentInfoHeader
          documentTitle={generatedContent?.title}
          documentData={documentData}
          generatedContent={generatedContent}
          currentPhase={currentPhase}
          onReviewClick={handleReviewClick}
          onExportClick={handleExport}
          onManualSave={handleManualSave}
          saveStatus={saveStatus}
          lastSaved={lastSaved}
          onChooseTemplate={handleChooseTemplate}
          showChooseTemplate={currentPhase === 'Review'} // Show after review phase
        />

        {/* Document Canvas Container - Optimized height without zoom controls */}
        <div className="bg-gray-50 overflow-hidden" style={{
          height: 'calc(100vh - 8.5rem)', // Desktop: workflow header (5rem) + simplified info header (3.5rem)
          minHeight: '400px' // Ensure minimum usable height on small screens
        }}>
          {/* Minimal Canvas with AI Content Integration */}
          <DocumentCanvasMinimal
            content={generatedContent}
            onContentChange={handleContentUpdate}
            isLoading={isGenerating}
            imageSuggestions={imageSuggestions}
            onOpenImageModal={handleOpenImageModal}
            onEditorReady={handleEditorReady}
          />
        </div>
      </main>

      {/* Image Selection Modal */}
      <ImageSelectionModal
        isOpen={isImageModalOpen}
        onClose={handleCloseImageModal}
        onImageSelect={handleImageSelect}
        imageSuggestions={imageSuggestions}
        chapterId={selectedChapterId}
        isReviewMode={false}
      />

      {/* Export Success Modal */}
      <ExportSuccessModal
        isOpen={showExportModal}
        onClose={handleExportModalClose}
        format={exportFormat}
        error={exportError}
        result={exportResult}
      />


    </div>
  );
};

export default DocumentEditor;
