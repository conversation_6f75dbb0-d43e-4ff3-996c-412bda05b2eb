import React, { useState, useRef, useEffect, useCallback } from 'react';
import imageOverlayService from '../../services/imageOverlayService.js';

import { prodLogger } from '../../utils/prodLogger.js';
/**
 * Interactive Template Canvas Component
 * Provides Canva-style text editing with click-to-select and drag-to-move functionality
 * for user-facing template previews
 */
const InteractiveTemplateCanvas = ({
  template = null,
  documentData = {},
  customizations = {},
  onCustomizationChange = null,
  onTextSelect = null,
  selectedOverlayId: externalSelectedOverlayId = null,
  className = '',
  isInteractive = true
}) => {
  const canvasRef = useRef(null);
  const containerRef = useRef(null);
  const [internalSelectedOverlayId, setInternalSelectedOverlayId] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [renderedCanvas, setRenderedCanvas] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredOverlayId, setHoveredOverlayId] = useState(null);

  // Use external selection if provided, otherwise use internal state
  const selectedOverlayId = externalSelectedOverlayId || internalSelectedOverlayId;
  const setSelectedOverlayId = externalSelectedOverlayId !== null ?
    (id) => onTextSelect?.(id) : setInternalSelectedOverlayId;

  // Get overlays from template
  const overlays = template?.text_overlays?.overlays || [];

  // Get template dimensions
  const templateWidth = template?.background_image_width || 1200;
  const templateHeight = template?.background_image_height || 1600;

  // Render template to canvas
  const renderTemplate = useCallback(async () => {
    if (!template) return;

    setIsLoading(true);
    try {
      const canvas = await imageOverlayService.renderTemplateWithCustomizations(
        template,
        documentData,
        customizations
      );

      setRenderedCanvas(canvas);

      // Draw the rendered template onto our display canvas
      if (canvasRef.current) {
        const displayCanvas = canvasRef.current;
        const displayCtx = displayCanvas.getContext('2d');

        // Set canvas dimensions to match template
        displayCanvas.width = templateWidth;
        displayCanvas.height = templateHeight;

        // Clear and draw the rendered template
        displayCtx.clearRect(0, 0, templateWidth, templateHeight);
        displayCtx.drawImage(canvas, 0, 0);
      }
    } catch (error) {
      prodLogger.error('Error rendering template:', error);
    } finally {
      setIsLoading(false);
    }
  }, [template, documentData, customizations, templateWidth, templateHeight]);

  // Initial render and re-render on changes
  useEffect(() => {
    renderTemplate();
  }, [renderTemplate]);

  // Draw interactive overlay
  useEffect(() => {
    drawInteractiveOverlay();
  }, [renderedCanvas, selectedOverlayId, hoveredOverlayId, overlays, customizations]);

  const drawInteractiveOverlay = () => {
    const canvas = canvasRef.current;
    if (!canvas || !renderedCanvas) return;

    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw the rendered template at full size
    ctx.drawImage(renderedCanvas, 0, 0);

    // Draw interactive overlays if interactive mode is enabled
    if (isInteractive && overlays.length > 0) {
      overlays.forEach((overlay) => {
        const isSelected = selectedOverlayId === overlay.id;
        const isHovered = hoveredOverlayId === overlay.id;
        const customization = customizations[overlay.id] || {};
        const position = customization.position || overlay.position;

        const x = position.x;
        const y = position.y;
        const width = position.width;
        const height = position.height;

        // Draw selection highlight
        if (isSelected) {
          // Draw selection background with subtle highlight
          ctx.fillStyle = 'rgba(59, 130, 246, 0.1)';
          ctx.fillRect(x - 2, y - 2, width + 4, height + 4);

          // Draw selection border
          ctx.strokeStyle = '#3B82F6';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.strokeRect(x - 2, y - 2, width + 4, height + 4);

          // Draw selection handles with better visibility
          const handleSize = 10;
          ctx.fillStyle = '#3B82F6';
          ctx.strokeStyle = '#FFFFFF';
          ctx.lineWidth = 2;

          const handles = [
            [x - handleSize/2, y - handleSize/2], // Top-left
            [x + width - handleSize/2, y - handleSize/2], // Top-right
            [x - handleSize/2, y + height - handleSize/2], // Bottom-left
            [x + width - handleSize/2, y + height - handleSize/2], // Bottom-right
            [x + width/2 - handleSize/2, y - handleSize/2], // Top-center
            [x + width/2 - handleSize/2, y + height - handleSize/2], // Bottom-center
            [x - handleSize/2, y + height/2 - handleSize/2], // Left-center
            [x + width - handleSize/2, y + height/2 - handleSize/2] // Right-center
          ];

          handles.forEach(([hx, hy]) => {
            // Draw handle with shadow effect
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(hx + 1, hy + 1, handleSize, handleSize);

            ctx.fillStyle = '#3B82F6';
            ctx.fillRect(hx, hy, handleSize, handleSize);
            ctx.strokeRect(hx, hy, handleSize, handleSize);
          });

          // Draw overlay label
          ctx.fillStyle = '#3B82F6';
          ctx.font = 'bold 12px Arial';
          ctx.textAlign = 'left';
          const labelText = overlay.id || 'Text Element';
          const labelY = y > 20 ? y - 8 : y + height + 16;
          ctx.fillText(labelText, x, labelY);
        } else if (isHovered) {
          // Draw hover highlight
          ctx.fillStyle = 'rgba(59, 130, 246, 0.05)';
          ctx.fillRect(x, y, width, height);

          ctx.strokeStyle = 'rgba(59, 130, 246, 0.6)';
          ctx.lineWidth = 2;
          ctx.setLineDash([]);
          ctx.strokeRect(x, y, width, height);

          // Show hover label
          ctx.fillStyle = 'rgba(59, 130, 246, 0.8)';
          ctx.font = '11px Arial';
          ctx.textAlign = 'left';
          const labelText = 'Click to select';
          const labelY = y > 20 ? y - 6 : y + height + 14;
          ctx.fillText(labelText, x, labelY);
        } else {
          // Draw subtle outline for non-selected, non-hovered overlays
          ctx.strokeStyle = 'rgba(59, 130, 246, 0.2)';
          ctx.lineWidth = 1;
          ctx.setLineDash([4, 4]);
          ctx.strokeRect(x, y, width, height);
        }
      });
    }
  };

  const getCanvasCoordinates = (e) => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };
    
    const rect = canvas.getBoundingClientRect();
    return {
      x: (e.clientX - rect.left) * (canvas.width / rect.width),
      y: (e.clientY - rect.top) * (canvas.height / rect.height)
    };
  };

  const getOverlayAtPosition = (x, y) => {
    if (!overlays.length) return null;

    // Check overlays from top to bottom
    for (let i = overlays.length - 1; i >= 0; i--) {
      const overlay = overlays[i];
      const customization = customizations[overlay.id] || {};
      const position = customization.position || overlay.position;

      const overlayX = position.x;
      const overlayY = position.y;
      const overlayWidth = position.width;
      const overlayHeight = position.height;

      if (x >= overlayX && x <= overlayX + overlayWidth &&
          y >= overlayY && y <= overlayY + overlayHeight) {
        return overlay.id;
      }
    }
    return null;
  };

  const handleMouseMove = (e) => {
    if (!isInteractive) return;

    if (isDragging && selectedOverlayId) {
      // Handle dragging
      e.preventDefault();
      const coords = getCanvasCoordinates(e);
      const deltaX = coords.x - dragStart.x;
      const deltaY = coords.y - dragStart.y;

      // Find the overlay being dragged
      const overlay = overlays.find(o => o.id === selectedOverlayId);
      if (!overlay) return;

      const currentCustomization = customizations[selectedOverlayId] || {};
      const currentPosition = currentCustomization.position || overlay.position;

      // Calculate new position with bounds checking
      const newX = Math.max(0, Math.min(
        templateWidth - currentPosition.width,
        currentPosition.x + deltaX
      ));
      const newY = Math.max(0, Math.min(
        templateHeight - currentPosition.height,
        currentPosition.y + deltaY
      ));

      // Update customization
      onCustomizationChange?.(selectedOverlayId, 'position', {
        ...currentPosition,
        x: newX,
        y: newY
      });

      setDragStart(coords);
    } else {
      // Handle hover detection
      const coords = getCanvasCoordinates(e);
      const overlayId = getOverlayAtPosition(coords.x, coords.y);
      setHoveredOverlayId(overlayId);
    }
  };

  const handleMouseDown = (e) => {
    if (!isInteractive) return;

    e.preventDefault();
    const coords = getCanvasCoordinates(e);
    const overlayId = getOverlayAtPosition(coords.x, coords.y);

    if (overlayId) {
      setSelectedOverlayId(overlayId);
      setIsDragging(true);
      setDragStart(coords);
      onTextSelect?.(overlayId);
    } else {
      setSelectedOverlayId(null);
      onTextSelect?.(null);
    }
  };



  const handleMouseUp = (e) => {
    if (isDragging) {
      e.preventDefault();
      setIsDragging(false);
    }
  };

  // Global mouse event listeners for drag operations
  useEffect(() => {
    if (isDragging) {
      const handleGlobalMouseMove = (e) => handleMouseMove(e);
      const handleGlobalMouseUp = (e) => handleMouseUp(e);

      document.addEventListener('mousemove', handleGlobalMouseMove);
      document.addEventListener('mouseup', handleGlobalMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove);
        document.removeEventListener('mouseup', handleGlobalMouseUp);
      };
    }
  }, [isDragging, selectedOverlayId, dragStart, customizations]);

  if (isLoading) {
    return (
      <div className={`interactive-template-canvas ${className}`} ref={containerRef}>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">Rendering template...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`interactive-template-canvas ${className}`} ref={containerRef}>
      <canvas
        ref={canvasRef}
        width={templateWidth}
        height={templateHeight}
        className={`${
          isInteractive
            ? isDragging
              ? 'cursor-grabbing'
              : hoveredOverlayId
                ? 'cursor-pointer'
                : 'cursor-default'
            : 'cursor-default'
        }`}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setHoveredOverlayId(null)}
        style={{
          userSelect: 'none',
          width: '100%',
          height: '100%',
          objectFit: 'contain'
        }}
      />
    </div>
  );
};

export default InteractiveTemplateCanvas;
