/**
 * DOCX Generation Service - Handles proper DOCX document generation
 * Replaces the RTF-based workaround with true DOCX format using the docx library
 *
 * Enhanced with HTML and markdown content processing capabilities
 * Enhanced with comprehensive error handling and retry logic
 */

import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  AlignmentType,
  HeadingLevel,
  ImageRun,
} from "docx";
import {
  processContent,
  convertToDocxParagraphs,
  createHeadingParagraph,
  createTextParagraph,
  createListParagraphs,
  processContentWithErrorHandling,
} from "./contentProcessingService.js";
import {
  processImages,
  createDetailedErrorReport,
} from "./errorHandlingService.js";
import {
  validateDocumentData,
  validateContent,
} from "../utils/validation.js";
import {
  detectContentType,
  parseHTMLContent,
  convertMarkdownToHTML,
  hasExistingChapterHeading,
  CONTENT_TYPES,
} from "../utils/contentProcessing.js";
import {
  categorizeError,
  createUserFriendlyErrorMessage,
  generateImageErrorMessage,
} from "../utils/errorHandling.js";
import { createLogger } from "../utils/logger.js";

import { prodLogger } from '../utils/prodLogger.js';
// Create logger for this service
const logger = createLogger("DocxGenerationService");



/**
 * Creates a basic DOCX document with title page and metadata
 * @param {Object} documentData - Document metadata (title, author, description)
 * @param {Object} processedContent - Processed content structure (optional for basic document)
 * @returns {Promise<{success: boolean, blob?: Blob, error?: string}>}
 */
export const generateDocxDocument = async (
  documentData,
  processedContent = null
) => {
  try {
    const { title, author, description } = documentData;

    // Create document with metadata, styling, and numbering
    const numbering = await createDocumentNumbering();
    const styles = await createDocumentStyles();

    const doc = new Document({
      creator: author || "RapidDoc AI",
      title: title || "Untitled Document",
      description: description || "",
      numbering: numbering,
      styles: styles,
      sections: [
        {
          children: createTitlePage(title, author, description),
        },
      ],
    });

    // Generate the DOCX blob
    const blob = await Packer.toBlob(doc);

    return {
      success: true,
      blob: blob,
    };
  } catch (error) {
    logger.error("DOCX generation failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Creates title page content for the DOCX document
 * @param {string} title - Document title
 * @param {string} author - Document author
 * @param {string} description - Document description
 * @param {Object} options - Additional options including cover template
 * @returns {Array} Array of Paragraph objects for title page
 */
export const createTitlePage = (title, author, description, options = {}) => {
  const titlePageContent = [];

  // Add some spacing at the top
  titlePageContent.push(
    new Paragraph({
      children: [new TextRun("")],
      spacing: { after: 400 },
    })
  );

  // Document title
  if (title) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: title,
            bold: true,
            size: 48, // 24pt font size (size is in half-points)
            color: "2c3e50",
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 400 },
      })
    );
  }

  // Author
  if (author) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `by ${author}`,
            size: 24, // 12pt font size
            color: "7f8c8d",
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 300 },
      })
    );
  }

  // Description
  if (description) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: description,
            italics: true,
            size: 20, // 10pt font size
            color: "95a5a6",
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      })
    );
  }

  return titlePageContent;
};

/**
 * Creates cover template page for DOCX document
 * @param {Object} template - Cover template configuration
 * @param {Object} documentData - Document metadata
 * @param {Object} options - Additional options
 * @returns {Promise<Array>} Array of Paragraph objects for cover page
 */
export const createCoverTemplatePage = async (template, documentData, options = {}) => {
  try {
    prodLogger.debug('🎨 Creating cover template page for DOCX', {
      templateId: template?.id,
      templateName: template?.name,
      documentTitle: documentData?.title
    });

    // Import cover preview service dynamically
    const coverPreviewService = await import('./coverPreviewService.js').then(module => module.default);

    // Generate cover image using the same system as preview
    const coverPreview = await coverPreviewService.generateCoverPreview(template, documentData, {
      quality: 0.95, // High quality for export
      format: 'png'
    });

    if (!coverPreview.coverImageData) {
      throw new Error('Failed to generate cover image');
    }

    // Convert base64 image to buffer for DOCX
    const imageBuffer = await convertBase64ToBuffer(coverPreview.coverImageData);

    // FULL-BLEED: Create full-page image paragraph for cover
    const coverImageParagraph = new Paragraph({
      children: [
        new ImageRun({
          data: imageBuffer,
          transformation: {
            width: 595, // Full A4 width in points (8.27 inches * 72 points/inch)
            height: 842, // Full A4 height in points (11.69 inches * 72 points/inch)
          },
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 0,
        after: 0,
        line: 0
      },
    });

    prodLogger.debug('✅ Cover template page created successfully for DOCX');
    return [coverImageParagraph];

  } catch (error) {
    prodLogger.error('❌ Error creating cover template page for DOCX:', error);

    // Fallback to simple text-based title page
    prodLogger.debug('🔄 Falling back to simple title page');
    return createSimpleTitlePage(documentData.title, documentData.author, documentData.description);
  }
};

/**
 * Creates simple text-based title page as fallback
 * @param {string} title - Document title
 * @param {string} author - Document author
 * @param {string} description - Document description
 * @returns {Array} Array of Paragraph objects for simple title page
 */
const createSimpleTitlePage = (title, author, description) => {
  const titlePageContent = [];

  // Add some spacing at the top
  titlePageContent.push(
    new Paragraph({
      children: [new TextRun("")],
      spacing: { after: 400 },
    })
  );

  // Document title
  if (title) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: title,
            bold: true,
            size: 48, // 24pt font size
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 300 },
      })
    );
  }

  // Author
  if (author) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `by ${author}`,
            size: 24, // 12pt font size
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      })
    );
  }

  // Description
  if (description) {
    titlePageContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: description,
            italics: true,
            size: 20, // 10pt font size
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: { after: 200 },
      })
    );
  }

  return titlePageContent;
};

/**
 * Convert base64 image data to buffer
 * @param {string} base64Data - Base64 image data (with or without data URL prefix)
 * @returns {Promise<Buffer>} Image buffer
 */
const convertBase64ToBuffer = async (base64Data) => {
  try {
    // Remove data URL prefix if present
    const base64String = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');

    // Convert to buffer
    if (typeof Buffer !== 'undefined') {
      // Node.js environment
      return Buffer.from(base64String, 'base64');
    } else {
      // Browser environment
      const binaryString = atob(base64String);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes;
    }
  } catch (error) {
    prodLogger.error('❌ Error converting base64 to buffer:', error);
    throw new Error('Failed to convert cover image for DOCX');
  }
};

/**
 * Creates document structure with chapters and content
 * @param {Object} documentData - Document metadata
 * @param {Object} processedContent - Processed content with chapters
 * @returns {Object} Document structure for docx library
 */
export const createDocumentStructure = (documentData, processedContent) => {
  const { title, author, description } = documentData;
  const chapters = processedContent?.chapters || [];

  const sections = [];

  // Title page section
  sections.push({
    children: createTitlePage(title, author, description),
    properties: {
      page: {
        pageNumbers: {
          start: 1,
          formatType: "decimal",
        },
      },
    },
  });

  // Chapter sections with proper page breaks
  chapters.forEach((chapter, index) => {
    const chapterContent = createChapterContent(chapter, index);
    sections.push({
      children: chapterContent,
      properties: {
        page: {
          pageNumbers: {
            start: index + 2, // Start after title page
            formatType: "decimal",
          },
        },
      },
    });
  });

  return {
    creator: author || "RapidDoc AI",
    title: title || "Untitled Document",
    description: description || "",
    sections: sections,
  };
};

/**
 * Creates document structure with unified content (no separate chapters)
 * @param {Object} documentData - Document metadata
 * @param {Array} processedContent - Array of processed content elements
 * @returns {Object} Document structure for docx library
 */
export const createUnifiedDocumentStructure = (
  documentData,
  processedContent
) => {
  const { title, author, description } = documentData;

  const sections = [];

  // Title page section
  sections.push({
    children: createTitlePage(title, author, description),
    properties: {
      page: {
        pageNumbers: {
          start: 1,
          formatType: "decimal",
        },
      },
    },
  });

  // Main content section with proper formatting
  if (processedContent && processedContent.length > 0) {
    const contentParagraphs = convertToDocxParagraphs(processedContent);
    sections.push({
      children: contentParagraphs,
      properties: {
        page: {
          pageNumbers: {
            start: 2,
            formatType: "decimal",
          },
        },
      },
    });
  }

  return {
    creator: author || "RapidDoc AI",
    title: title || "Untitled Document",
    description: description || "",
    sections: sections,
  };
};

/**
 * Creates content for a single chapter with proper formatting and page breaks
 * @param {Object} chapter - Chapter data
 * @param {number} index - Chapter index
 * @returns {Array} Array of Paragraph objects for the chapter
 */
export const createChapterContent = (chapter, index) => {
  const chapterContent = [];

  // Chapter title with proper styling
  const chapterTitle =
    chapter.title || `Chapter ${chapter.number || index + 1}`;
  const chapterNumber = chapter.number || index + 1;

  // Check if chapter content already contains a chapter heading
  const contentString = typeof chapter.content === 'string' ? chapter.content : '';
  const hasHeading = hasExistingChapterHeading(contentString, chapterNumber, chapterTitle);

  // Only add programmatic chapter heading if not already present in content
  if (!hasHeading) {
    chapterContent.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Chapter ${chapterNumber}: ${chapterTitle}`,
            bold: true,
            size: 32, // 16pt font size
            color: "000000",
          }),
        ],
        heading: HeadingLevel.HEADING_1,
        spacing: {
          before: 400, // Space before chapter title
          after: 300, // Space after chapter title
        },
        pageBreakBefore: index > 0, // Add page break before chapter (except first)
      })
    );
  } else {
    // If content has heading, still add page break for chapters after the first
    if (index > 0) {
      chapterContent.push(
        new Paragraph({
          children: [new TextRun({ text: "" })],
          pageBreakBefore: true,
        })
      );
    }
  }

  // Process chapter content if it exists
  if (chapter.content) {
    // If content is a string, treat it as markdown/HTML and process it
    if (typeof chapter.content === "string") {
      const contentParagraphs = processChapterContentString(chapter.content);
      chapterContent.push(...contentParagraphs);
    }
    // If content is already processed content array
    else if (Array.isArray(chapter.content)) {
      const contentParagraphs = convertToDocxParagraphs(chapter.content);
      chapterContent.push(...contentParagraphs);
    }
    // Fallback for other content types
    else {
      chapterContent.push(
        new Paragraph({
          children: [
            new TextRun({
              text: String(chapter.content),
              size: 22, // 11pt font size
            }),
          ],
          spacing: { after: 200 },
        })
      );
    }
  }

  return chapterContent;
};

/**
 * Process chapter content string (markdown/HTML) into DOCX paragraphs
 * @param {string} contentString - Chapter content as string
 * @returns {Array} Array of DOCX Paragraph objects
 */
export const processChapterContentString = (contentString) => {
  // Handle null/undefined/empty content
  if (!contentString || typeof contentString !== "string") {
    return [
      new Paragraph({
        children: [
          new TextRun({
            text: contentString || "No content",
            size: 22,
          }),
        ],
        spacing: { after: 200 },
      }),
    ];
  }

  try {
    // Detect content type and process accordingly
    const contentType = detectContentType(contentString);

    let processedContent = [];
    if (contentType === "html") {
      processedContent = parseHTMLContent(contentString);
    } else {
      // For markdown, convert to HTML first then process
      const htmlContent = convertMarkdownToHTML(contentString);
      processedContent = parseHTMLContent(htmlContent);
    }

    const docxParagraphs = convertToDocxParagraphs(processedContent);

    // If no paragraphs were generated, create a fallback
    if (!docxParagraphs || docxParagraphs.length === 0) {
      return [
        new Paragraph({
          children: [
            new TextRun({
              text: contentString,
              size: 22,
            }),
          ],
          spacing: { after: 200 },
        }),
      ];
    }

    return docxParagraphs;
  } catch (error) {
    logger.error("Error processing chapter content string:", error);
    // Fallback to simple paragraph
    return [
      new Paragraph({
        children: [
          new TextRun({
            text: contentString || "Error processing content",
            size: 22,
          }),
        ],
        spacing: { after: 200 },
      }),
    ];
  }
};

/**
 * Downloads the generated DOCX blob as a file
 * @param {Blob} blob - The DOCX blob to download
 * @param {string} filename - The filename for the download
 */
export const downloadDocxFile = (blob, filename) => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename.endsWith(".docx") ? filename : `${filename}.docx`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * Generate DOCX document from HTML or markdown content with proper formatting
 * @param {Object} documentData - Document metadata (title, author, description)
 * @param {string} content - HTML or markdown content to process
 * @param {string} contentType - Type of content ('html' or 'markdown', auto-detected if not provided)
 * @returns {Promise<{success: boolean, blob?: Blob, error?: string}>}
 */
export const generateDocxFromContent = async (
  documentData,
  content,
  contentType = null
) => {
  try {
    const { title, author, description } = documentData;

    // Auto-detect content type if not provided
    const detectedType = contentType || detectContentType(content);

    // Process the content using our content processing service
    const processedResult = await processContent(content, detectedType);

    if (processedResult.error) {
      return {
        success: false,
        error: `Content processing failed: ${processedResult.error}`,
      };
    }

    // Convert processed content to DOCX paragraphs
    const contentParagraphs = convertToDocxParagraphs(
      processedResult.processedContent
    );

    // Create document sections
    const sections = [];

    // Title page section
    sections.push({
      children: createTitlePage(title, author, description),
    });

    // Content section with proper page break
    if (contentParagraphs.length > 0) {
      sections.push({
        children: contentParagraphs,
        properties: {
          page: {
            pageNumbers: {
              start: 2,
              formatType: "decimal",
            },
          },
        },
      });
    }

    // Create document with metadata and styling
    const numbering = await createDocumentNumbering();
    const styles = await createDocumentStyles();

    const doc = new Document({
      creator: author || "RapidDoc AI",
      title: title || "Untitled Document",
      description: description || "",
      numbering: numbering,
      styles: styles,
      sections: sections,
    });

    // Generate the DOCX blob
    const blob = await Packer.toBlob(doc);

    return {
      success: true,
      blob: blob,
      processedContent: processedResult,
      contentType: detectedType,
    };
  } catch (error) {
    logger.error("DOCX generation from content failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate DOCX document with embedded images from HTML or markdown content
 * Enhanced with comprehensive error handling and retry logic
 * @param {Object} documentData - Document metadata (title, author, description)
 * @param {string} content - HTML or markdown content to process
 * @param {string} contentType - Type of content ('html' or 'markdown', auto-detected if not provided)
 * @param {Object} options - Generation options including contentSource flag
 * @returns {Promise<{success: boolean, blob?: Blob, error?: string, imageStats?: Object, userMessage?: string}>}
 */
export const generateDocxWithImages = async (
  documentData,
  content,
  contentType = null,
  options = {}
) => {
  const operationContext = {
    operation: "generateDocxWithImages",
    documentTitle: documentData?.title || "Unknown",
    contentLength: content?.length || 0,
    contentType: contentType,
  };

  try {
    // Validate inputs using the statically imported function
    const docValidation = validateDocumentData(documentData);
    if (!docValidation.isValid) {
      return {
        success: false,
        error: `Document validation failed: ${docValidation.errors.join(", ")}`,
        userMessage: "Please check your document information and try again.",
      };
    }

    const { title, author, description } = documentData;

    // Auto-detect content type if not provided
    const detectedType = contentType || detectContentType(content);

    // Note: Duplicate chapter heading prevention is now handled in the content extraction phase

    // Validate content
    const contentValidation = validateContent(content, detectedType);
    if (!contentValidation.isValid) {
      return {
        success: false,
        error: `Content validation failed: ${contentValidation.errors.join(
          ", "
        )}`,
        userMessage:
          "The document content appears to be invalid. Please check your content and try again.",
      };
    }

    logger.info(`Starting DOCX generation with images for "${title}"`);

    // Process content with error handling
    const processedResult = await processContentWithErrorHandling(
      content,
      detectedType
    );
    if (!processedResult.success) {
      return {
        success: false,
        error: `Content processing failed: ${processedResult.errors.join(
          ", "
        )}`,
        userMessage:
          "Failed to process the document content. The content may be corrupted or in an unsupported format.",
      };
    }

    // Extract and process images with comprehensive error handling
    let imageResult = {
      totalImages: 0,
      successCount: 0,
      failureCount: 0,
      processedImages: [],
      failedImages: [],
    };

    try {
      // Extract images from content
      const extractedImages = processedResult.images;

      if (extractedImages && extractedImages.length > 0) {
        logger.info(`Found ${extractedImages.length} images to process`);

        // Process images with enhanced error handling and retry logic
        const imageProcessingOptions = options.imageProcessing || {
          concurrency: 3,
          maxRetries: 3,
          timeout: 15000,
          skipOnError: true,
          logProgress: true,
        };

        prodLogger.debug("📄 DOCX: Processing", extractedImages.length, "images");

        imageResult = await processImages(
          extractedImages,
          imageProcessingOptions
        );

        if (imageResult.failureCount > 0) {
          const errorMessage = generateImageErrorMessage(imageResult);
          logger.warn(errorMessage);

          // Create detailed error report for logging
          const errorReport = createDetailedErrorReport(imageResult);
          logger.debug(
            "Image processing error report:",
            JSON.stringify(errorReport, null, 2)
          );
        }

        logger.info(
          `Image processing complete: ${imageResult.successCount}/${imageResult.totalImages} successful`
        );
      } else {
        logger.info("No images found in content");
      }
    } catch (imageError) {
      logger.warn(
        "Image processing failed, continuing without images:",
        imageError.message
      );
      // Continue without images rather than failing completely
      imageResult = {
        totalImages: 0,
        successCount: 0,
        failureCount: 0,
        processedImages: [],
        failedImages: [],
        errors: [imageError.message],
      };
    }

    // Convert processed content to DOCX paragraphs with embedded images
    let contentParagraphs = [];
    try {
      contentParagraphs = await convertToDocxParagraphsWithImages(
        processedResult.processedContent,
        imageResult.processedImages
      );
    } catch (conversionError) {
      logger.warn(
        "Enhanced paragraph conversion failed, using fallback:",
        conversionError.message
      );
      // Fallback to basic paragraph conversion
      contentParagraphs = convertToDocxParagraphs(
        processedResult.processedContent
      );
    }

    // Ensure we have some content
    if (contentParagraphs.length === 0) {
      contentParagraphs = [
        new Paragraph({
          children: [
            new TextRun({
              text: "Document content could not be processed.",
              italics: true,
            }),
          ],
        }),
      ];
    }

    // Create document sections
    const sections = [];

    // Title page section - with cover template support
    try {
      const { selectedTemplate } = options;
      let titlePageChildren;

      if (selectedTemplate) {
        prodLogger.debug('🎨 Using cover template for DOCX title page:', selectedTemplate.name);
        // Use cover template for title page
        titlePageChildren = await createCoverTemplatePage(selectedTemplate, documentData, options);
      } else {
        prodLogger.debug('📄 Using simple text title page for DOCX');
        // Use simple text-based title page
        titlePageChildren = createTitlePage(title, author, description, options);
      }

      sections.push({
        children: titlePageChildren,
        properties: {
          page: {
            margin: {
              top: 0,
              right: 0,
              bottom: 0,
              left: 0,
            },
          },
        },
      });
    } catch (titleError) {
      logger.warn(
        "Title page creation failed, using fallback:",
        titleError.message
      );
      sections.push({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: title || "Untitled Document",
                bold: true,
                size: 32,
              }),
            ],
          }),
        ],
      });
    }

    // Use unified content approach - content processing handles headings appropriately
    // Content section with processed content
    sections.push({
      children: contentParagraphs,
      properties: {
        page: {
          pageNumbers: {
            start: 2,
            formatType: "decimal",
          },
        },
      },
    });

    // Create document with error handling for each component
    let doc;
    try {
      // Create numbering and styles configurations
      const numbering = await createDocumentNumbering();
      const styles = await createDocumentStyles();

      doc = new Document({
        creator: author || "RapidDoc AI",
        title: title || "Untitled Document",
        description: description || "",
        numbering: numbering,
        styles: styles,
        sections: sections,
      });
    } catch (docError) {
      logger.warn(
        "Enhanced document creation failed, using basic structure:",
        docError.message
      );
      // Fallback to basic document structure
      doc = new Document({
        creator: author || "RapidDoc AI",
        title: title || "Untitled Document",
        sections: sections,
      });
    }

    // Generate the DOCX blob with timeout protection
    let blob;
    try {
      const blobPromise = Packer.toBlob(doc);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error("Document generation timeout")),
          30000
        )
      );

      blob = await Promise.race([blobPromise, timeoutPromise]);
    } catch (blobError) {
      logger.error("DOCX blob generation failed:", blobError.message);
      return {
        success: false,
        error: `Document generation failed: ${blobError.message}`,
        userMessage:
          "Failed to generate the document. The document may be too large or complex.",
      };
    }

    // Generate user-friendly message about image processing
    let userMessage = "Document generated successfully";
    if (imageResult.totalImages > 0) {
      if (imageResult.failureCount > 0) {
        const imageErrorMsg = generateImageErrorMessage(imageResult);
        userMessage += `. ${imageErrorMsg}`;
      } else {
        userMessage += ` with ${imageResult.successCount} images embedded`;
      }
    }

    return {
      success: true,
      blob: blob,
      processedContent: processedResult,
      contentType: detectedType,
      userMessage: userMessage,
      imageStats: {
        totalImages: imageResult.totalImages,
        successfulImages: imageResult.successCount,
        failedImages: imageResult.failureCount,
        processedImages: imageResult.processedImages,
        failedImagesList: imageResult.failedImages,
      },
    };
  } catch (error) {
    logger.error("DOCX generation with images failed:", error);

    // Categorize error and create user-friendly message
    const errorType = categorizeError(error);
    const userMessage = createUserFriendlyErrorMessage(error);

    // Create detailed error report
    const errorDetails = {
      errorType,
      message: error.message,
      stack: error.stack,
      context: operationContext,
    };

    prodLogger.debug("Error details:", JSON.stringify(errorDetails, null, 2));

    return {
      success: false,
      error: error.message,
      userMessage: userMessage,
      errorType: errorType,
      context: operationContext,
    };
  }
};

/**
 * Convert processed content to DOCX Paragraph objects with embedded images
 * @param {Array} processedContent - Array of processed content objects
 * @param {Array} processedImages - Array of processed image objects with data
 * @returns {Promise<Array>} Array of DOCX Paragraph objects with embedded images
 */
export const convertToDocxParagraphsWithImages = async (
  processedContent,
  processedImages = []
) => {
  if (!Array.isArray(processedContent)) {
    return [];
  }

  // Create a map of image sources to processed image data for quick lookup
  const imageMap = new Map();
  processedImages.forEach((image) => {
    imageMap.set(image.src, image);
  });

  const paragraphs = [];

  processedContent.forEach((content) => {
    switch (content.type) {
      case "heading":
      case CONTENT_TYPES.HEADING:
        paragraphs.push(createHeadingParagraph(content));
        break;

      case "paragraph":
      case CONTENT_TYPES.PARAGRAPH:
        paragraphs.push(createTextParagraph(content));
        break;

      case "list":
      case CONTENT_TYPES.LIST:
        paragraphs.push(...createListParagraphs(content));
        break;

      case "image":
      case CONTENT_TYPES.IMAGE:
        // Find the processed image data for this image
        const processedImage = imageMap.get(content.src);
        if (processedImage) {
          paragraphs.push(createImageParagraph(processedImage));
        } else {
          // Fallback to placeholder if image wasn't processed
          paragraphs.push(createImagePlaceholderParagraph(content));
        }
        break;

      default:
        // Fallback to paragraph
        paragraphs.push(createTextParagraph(content));
        break;
    }
  });

  return paragraphs;
};

/**
 * Image sizing configuration for DOCX documents
 */
export const IMAGE_SIZE_CONFIG = {
  DEFAULT_WIDTH: 600,
  DEFAULT_HEIGHT: 400,
  MAX_WIDTH: 800,
  MAX_HEIGHT: 600,
  SMALL_WIDTH: 300,
  SMALL_HEIGHT: 200,
  MEDIUM_WIDTH: 500,
  MEDIUM_HEIGHT: 350,
  LARGE_WIDTH: 700,
  LARGE_HEIGHT: 500,
};

/**
 * Calculate appropriate image dimensions for DOCX embedding
 * @param {Object} image - Image object with width, height, and size class
 * @returns {Object} Calculated dimensions with width and height
 */
export const calculateImageDimensions = (image) => {
  let targetWidth = IMAGE_SIZE_CONFIG.DEFAULT_WIDTH;
  let targetHeight = IMAGE_SIZE_CONFIG.DEFAULT_HEIGHT;

  // Check for size class from TipTap editor
  if (image.class) {
    const sizeClass = image.class.toLowerCase();
    if (sizeClass.includes("small")) {
      targetWidth = IMAGE_SIZE_CONFIG.SMALL_WIDTH;
      targetHeight = IMAGE_SIZE_CONFIG.SMALL_HEIGHT;
    } else if (sizeClass.includes("medium")) {
      targetWidth = IMAGE_SIZE_CONFIG.MEDIUM_WIDTH;
      targetHeight = IMAGE_SIZE_CONFIG.MEDIUM_HEIGHT;
    } else if (sizeClass.includes("large")) {
      targetWidth = IMAGE_SIZE_CONFIG.LARGE_WIDTH;
      targetHeight = IMAGE_SIZE_CONFIG.LARGE_HEIGHT;
    }
  }

  // Use explicit dimensions if provided
  if (image.width && image.height) {
    targetWidth = Math.min(parseInt(image.width), IMAGE_SIZE_CONFIG.MAX_WIDTH);
    targetHeight = Math.min(
      parseInt(image.height),
      IMAGE_SIZE_CONFIG.MAX_HEIGHT
    );
  } else if (image.width) {
    targetWidth = Math.min(parseInt(image.width), IMAGE_SIZE_CONFIG.MAX_WIDTH);
    // Maintain aspect ratio if only width is provided
    targetHeight = Math.round(targetWidth * 0.75); // Assume 4:3 aspect ratio
  } else if (image.height) {
    targetHeight = Math.min(
      parseInt(image.height),
      IMAGE_SIZE_CONFIG.MAX_HEIGHT
    );
    // Maintain aspect ratio if only height is provided
    targetWidth = Math.round(targetHeight * 1.33); // Assume 4:3 aspect ratio
  }

  return {
    width: targetWidth,
    height: targetHeight,
  };
};

/**
 * Create DOCX image paragraph with embedded image data
 * @param {Object} image - Processed image object with data and metadata
 * @returns {Paragraph} DOCX Paragraph object with embedded image
 */
export const createImageParagraph = (image) => {
  try {
    if (!image.data || !image.downloadSuccess) {
      // Fallback to placeholder if image data is not available
      return createImagePlaceholderParagraph(image);
    }

    const dimensions = calculateImageDimensions(image);

    const imageRun = new ImageRun({
      data: image.data,
      transformation: {
        width: dimensions.width,
        height: dimensions.height,
      },
      altText: {
        title: image.alt || "Image",
        description: image.alt || "Embedded image from document",
      },
    });

    return new Paragraph({
      children: [imageRun],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 200,
        after: 200,
      },
    });
  } catch (error) {
    logger.error("Error creating image paragraph:", error);
    // Fallback to placeholder on error
    return createImagePlaceholderParagraph(image);
  }
};

/**
 * Create image placeholder paragraph for failed image downloads
 * @param {Object} image - Image object (may have failed to download)
 * @returns {Paragraph} DOCX Paragraph object with image placeholder
 */
export const createImagePlaceholderParagraph = (image) => {
  const placeholderText = image.error
    ? `[Image unavailable: ${image.alt || "Image"}]`
    : `[Image: ${image.alt || "Image"}]`;

  return new Paragraph({
    children: [
      new TextRun({
        text: placeholderText,
        italics: true,
        color: "666666",
      }),
    ],
    alignment: AlignmentType.CENTER,
    spacing: { after: 200 },
  });
};

/**
 * Generate and download DOCX document from HTML or markdown content
 * @param {Object} documentData - Document metadata
 * @param {string} content - HTML or markdown content to process
 * @param {string} contentType - Type of content ('html' or 'markdown', auto-detected if not provided)
 * @returns {Promise<{success: boolean, message?: string, error?: string}>}
 */
export const generateAndDownloadDocxFromContent = async (
  documentData,
  content,
  contentType = null
) => {
  try {
    const result = await generateDocxFromContent(
      documentData,
      content,
      contentType
    );

    if (!result.success) {
      return result;
    }

    const filename = documentData.title || "document";
    downloadDocxFile(result.blob, filename);

    return {
      success: true,
      message: "DOCX document generated and downloaded successfully",
      processedContent: result.processedContent,
      contentType: result.contentType,
    };
  } catch (error) {
    logger.error("DOCX generation and download from content failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Generate and download DOCX document with embedded images
 * Enhanced with comprehensive error handling and user feedback
 * @param {Object} documentData - Document metadata
 * @param {string} content - HTML or markdown content to process
 * @param {string} contentType - Type of content ('html' or 'markdown', auto-detected if not provided)
 * @returns {Promise<{success: boolean, message?: string, error?: string, userMessage?: string, imageStats?: Object}>}
 */
export const generateAndDownloadDocxWithImages = async (
  documentData,
  content,
  contentType = null
) => {
  const operationStart = Date.now();

  try {
    logger.info("Starting DOCX generation and download with images...");

    // Generate the document with comprehensive error handling
    const result = await generateDocxWithImages(
      documentData,
      content,
      contentType
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error,
        userMessage:
          result.userMessage ||
          "Failed to generate the document. Please try again.",
        context: result.context,
      };
    }

    // Attempt to download the file with error handling
    const filename = documentData.title || "document";
    try {
      downloadDocxFile(result.blob, filename);
      logger.info(`DOCX download initiated for "${filename}"`);
    } catch (downloadError) {
      logger.error("Download failed:", downloadError.message);

      // Provide fallback download method or instructions
      let downloadMessage =
        "Document generated successfully, but download failed. ";
      if (downloadError.message.includes("blocked")) {
        downloadMessage +=
          "Please check your browser's download settings and allow downloads from this site.";
      } else {
        downloadMessage += "Please try again or check your browser settings.";
      }

      return {
        success: false,
        error: `Download failed: ${downloadError.message}`,
        userMessage: downloadMessage,
        blob: result.blob, // Provide blob for manual handling
      };
    }

    const operationTime = Date.now() - operationStart;
    logger.info(`DOCX generation and download completed in ${operationTime}ms`);

    // Use the user message from generation result or create a summary
    let message =
      result.userMessage ||
      "DOCX document generated and downloaded successfully";

    // Add timing information for large documents
    if (operationTime > 5000) {
      message += ` (completed in ${Math.round(operationTime / 1000)}s)`;
    }

    return {
      success: true,
      message: message,
      userMessage: message,
      processedContent: result.processedContent,
      contentType: result.contentType,
      imageStats: result.imageStats,
      operationTime: operationTime,
    };
  } catch (error) {
    const operationTime = Date.now() - operationStart;
    logger.error("DOCX generation and download with images failed:", error);

    // Use enhanced error categorization and user-friendly messages
    const errorType = categorizeError(error);
    const userMessage = createUserFriendlyErrorMessage(error);

    // Create detailed error report for logging
    const errorDetails = {
      errorType,
      message: error.message,
      stack: error.stack,
      operationTime,
      context: {
        operation: "generateAndDownloadDocxWithImages",
        documentTitle: documentData?.title || "Unknown",
        contentLength: content?.length || 0,
        contentType: contentType,
      },
    };

    prodLogger.debug("Error details:", JSON.stringify(errorDetails, null, 2));

    return {
      success: false,
      error: error.message,
      userMessage: userMessage,
      errorType: errorType,
      operationTime: operationTime,
      context: {
        operation: "generateAndDownloadDocxWithImages",
        documentTitle: documentData?.title || "Unknown",
        contentLength: content?.length || 0,
        contentType: contentType,
      },
    };
  }
};

/**
 * Create document numbering definitions for lists
 * @returns {Object} Numbering configuration for DOCX document
 */
export const createDocumentNumbering = async () => {
  return {
    config: [
      {
        reference: "bullet-list",
        levels: [
          {
            level: 0,
            format: "bullet",
            text: "•",
            alignment: "left",
            style: {
              paragraph: {
                indent: { left: 720, hanging: 360 },
              },
            },
          },
        ],
      },
      {
        reference: "ordered-list",
        levels: [
          {
            level: 0,
            format: "decimal",
            text: "%1.",
            alignment: "left",
            style: {
              paragraph: {
                indent: { left: 720, hanging: 360 },
              },
            },
          },
        ],
      },
    ],
  };
};

/**
 * Create document styles for consistent formatting
 * @returns {Object} Styles configuration for DOCX document
 */
export const createDocumentStyles = async () => {
  return {
    paragraphStyles: [
      {
        id: "Normal",
        name: "Normal",
        basedOn: "Normal",
        next: "Normal",
        run: {
          size: 22, // 11pt
          font: "Calibri",
        },
        paragraph: {
          spacing: {
            after: 120,
            line: 276,
            lineRule: "auto",
          },
        },
      },
      {
        id: "Heading1",
        name: "Heading 1",
        basedOn: "Normal",
        next: "Normal",
        run: {
          size: 32, // 16pt
          bold: true,
          color: "000000",
          font: "Calibri",
        },
        paragraph: {
          spacing: {
            before: 400,
            after: 300,
          },
        },
      },
      {
        id: "Heading2",
        name: "Heading 2",
        basedOn: "Normal",
        next: "Normal",
        run: {
          size: 28, // 14pt
          bold: true,
          color: "000000",
          font: "Calibri",
        },
        paragraph: {
          spacing: {
            before: 300,
            after: 200,
          },
        },
      },
      {
        id: "Heading3",
        name: "Heading 3",
        basedOn: "Normal",
        next: "Normal",
        run: {
          size: 24, // 12pt
          bold: true,
          color: "000000",
          font: "Calibri",
        },
        paragraph: {
          spacing: {
            before: 200,
            after: 160,
          },
        },
      },
    ],
  };
};

/**
 * Main function to generate and download a DOCX document
 * @param {Object} documentData - Document metadata
 * @param {Object} processedContent - Processed content (optional)
 * @returns {Promise<{success: boolean, message?: string, error?: string}>}
 */
export const generateAndDownloadDocx = async (
  documentData,
  processedContent = null
) => {
  try {
    const result = await generateDocxDocument(documentData, processedContent);

    if (!result.success) {
      return result;
    }

    const filename = documentData.title || "document";
    downloadDocxFile(result.blob, filename);

    return {
      success: true,
      message: "DOCX document generated and downloaded successfully",
    };
  } catch (error) {
    logger.error("DOCX generation and download failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};
