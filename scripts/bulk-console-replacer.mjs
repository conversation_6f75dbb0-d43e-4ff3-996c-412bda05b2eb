#!/usr/bin/env node

/**
 * Bulk Console Statement Replacer
 * Automatically replaces console statements with production logger
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, "..");
const srcDir = path.join(projectRoot, "src");

// File extensions to process
const FILE_EXTENSIONS = [".js", ".jsx", ".ts", ".tsx"];

// Console method mappings
const CONSOLE_MAPPINGS = {
  "console.log": "prodLogger.debug",
  "console.info": "prodLogger.info",
  "console.warn": "prodLogger.warn",
  "console.error": "prodLogger.error",
  "console.debug": "prodLogger.debug",
};

/**
 * Get all source files
 */
function getAllFiles(dir, extensions = FILE_EXTENSIONS) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (
        stat.isDirectory() &&
        !item.startsWith(".") &&
        item !== "node_modules"
      ) {
        traverse(fullPath);
      } else if (
        stat.isFile() &&
        extensions.some((ext) => item.endsWith(ext))
      ) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * Check if file already imports prodLogger
 */
function hasProdLoggerImport(content) {
  return (
    content.includes("prodLogger") ||
    content.includes("from './prodLogger") ||
    content.includes("from '../utils/prodLogger")
  );
}

/**
 * Add prodLogger import to file
 */
function addProdLoggerImport(content, filePath) {
  // Calculate relative path to prodLogger
  const relativePath = path.relative(
    path.dirname(filePath),
    path.join(srcDir, "utils", "prodLogger.js")
  );
  const importPath = relativePath.startsWith(".")
    ? relativePath
    : "./" + relativePath;

  // Find existing imports
  const importRegex = /import\s+.*?\s+from\s+['"][^'"]+['"];?\s*\n/g;
  const imports = content.match(importRegex) || [];

  if (imports.length > 0) {
    // Add after last import
    const lastImport = imports[imports.length - 1];
    const lastImportIndex = content.lastIndexOf(lastImport) + lastImport.length;
    const prodLoggerImport = `import { prodLogger } from '${importPath}';\n`;

    return (
      content.slice(0, lastImportIndex) +
      prodLoggerImport +
      content.slice(lastImportIndex)
    );
  } else {
    // Add at the beginning
    return `import { prodLogger } from '${importPath}';\n\n` + content;
  }
}

/**
 * Replace console statements in content
 */
function replaceConsoleStatements(content) {
  let modifiedContent = content;
  let replacementCount = 0;

  for (const [oldPattern, newPattern] of Object.entries(CONSOLE_MAPPINGS)) {
    const regex = new RegExp(oldPattern.replace(".", "\\."), "g");
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, newPattern);
      replacementCount += matches.length;
    }
  }

  return { content: modifiedContent, count: replacementCount };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  const content = fs.readFileSync(filePath, "utf8");
  const relativePath = path.relative(projectRoot, filePath);

  // Skip files that don't have console statements
  if (!content.includes("console.")) {
    return { processed: false, replacements: 0 };
  }

  // Skip test files for now (they might need console for testing)
  if (filePath.includes("__tests__") || filePath.includes(".test.")) {
    return { processed: false, replacements: 0, reason: "test file" };
  }

  // Skip the prodLogger file itself
  if (filePath.includes("prodLogger.js")) {
    return { processed: false, replacements: 0, reason: "prodLogger file" };
  }

  let modifiedContent = content;

  // Add prodLogger import if needed
  if (!hasProdLoggerImport(content)) {
    try {
      modifiedContent = addProdLoggerImport(modifiedContent, filePath);
    } catch (error) {
      return {
        processed: false,
        replacements: 0,
        reason: "import error: " + error.message,
      };
    }
  }

  // Replace console statements
  const { content: finalContent, count } =
    replaceConsoleStatements(modifiedContent);

  if (count > 0) {
    fs.writeFileSync(filePath, finalContent, "utf8");
    return { processed: true, replacements: count };
  }

  return { processed: false, replacements: 0 };
}

/**
 * Main processing function
 */
function processAllFiles() {
  console.log("🔄 Processing console statements in source files...\n");

  const files = getAllFiles(srcDir);
  let totalProcessed = 0;
  let totalReplacements = 0;
  const results = [];

  for (const file of files) {
    const result = processFile(file);
    const relativePath = path.relative(projectRoot, file);

    if (result.processed) {
      totalProcessed++;
      totalReplacements += result.replacements;
      results.push({
        file: relativePath,
        replacements: result.replacements,
      });
      console.log(`✅ ${relativePath}: ${result.replacements} replacements`);
    } else if (result.reason) {
      console.log(`⏭️  ${relativePath}: skipped (${result.reason})`);
    }
  }

  console.log(`\n📊 SUMMARY`);
  console.log(`==========`);
  console.log(`Files processed: ${totalProcessed}`);
  console.log(`Total replacements: ${totalReplacements}`);
  console.log(`Files analyzed: ${files.length}`);

  if (results.length > 0) {
    console.log(`\nTop files by replacements:`);
    results
      .sort((a, b) => b.replacements - a.replacements)
      .slice(0, 10)
      .forEach((result) => {
        console.log(`  ${result.file}: ${result.replacements} replacements`);
      });
  }

  console.log(`\n💡 Next steps:`);
  console.log(`1. Review the changes in your git diff`);
  console.log(`2. Test the application to ensure it still works`);
  console.log(`3. Run the production cleanup script again to verify`);
}

// Run the processor
try {
  processAllFiles();
} catch (error) {
  console.error("❌ Processing failed:", error.message);
  process.exit(1);
}
