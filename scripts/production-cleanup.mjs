#!/usr/bin/env node

/**
 * Production Cleanup Script
 * Identifies and reports unused dependencies, console statements, and other cleanup items
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const projectRoot = path.join(__dirname, "..");
const srcDir = path.join(projectRoot, "src");
const packageJsonPath = path.join(projectRoot, "package.json");

console.log("🔍 DocForge AI Production Cleanup Analysis");
console.log("==========================================\n");

// Read package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
const dependencies = Object.keys(packageJson.dependencies || {});
const devDependencies = Object.keys(packageJson.devDependencies || {});

// File extensions to analyze
const FILE_EXTENSIONS = [".js", ".jsx", ".ts", ".tsx"];

/**
 * Recursively get all files with specified extensions
 */
function getAllFiles(dir, extensions = FILE_EXTENSIONS) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (
        stat.isDirectory() &&
        !item.startsWith(".") &&
        item !== "node_modules"
      ) {
        traverse(fullPath);
      } else if (
        stat.isFile() &&
        extensions.some((ext) => item.endsWith(ext))
      ) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * Analyze imports in all source files
 */
function analyzeImports() {
  const files = getAllFiles(srcDir);
  const usedPackages = new Set();
  const consoleStatements = [];
  const securityIssues = [];

  console.log("📦 Analyzing imports and code patterns...\n");

  for (const file of files) {
    const content = fs.readFileSync(file, "utf8");
    const relativePath = path.relative(projectRoot, file);

    // Find imports
    const importMatches =
      content.match(/import\s+.*?\s+from\s+['"]([^'"]+)['"]/g) || [];

    for (const importMatch of importMatches) {
      const packageMatch = importMatch.match(/from\s+['"]([^'"]+)['"]/);
      if (packageMatch) {
        const importPath = packageMatch[1];
        // Extract package name (handle scoped packages)
        const packageName = importPath.startsWith("@")
          ? importPath.split("/").slice(0, 2).join("/")
          : importPath.split("/")[0];

        if (!importPath.startsWith(".") && !importPath.startsWith("/")) {
          usedPackages.add(packageName);
        }
      }
    }

    // Find console statements
    const consoleMatches =
      content.match(/console\.(log|warn|error|debug|info)/g) || [];
    if (consoleMatches.length > 0) {
      consoleStatements.push({
        file: relativePath,
        count: consoleMatches.length,
      });
    }

    // Find security issues
    if (content.includes("Function(")) {
      securityIssues.push({
        file: relativePath,
        issue: "Function() constructor usage",
        severity: "HIGH",
      });
    }

    if (content.includes("eval(")) {
      securityIssues.push({
        file: relativePath,
        issue: "eval() usage",
        severity: "CRITICAL",
      });
    }

    if (content.includes("innerHTML =")) {
      securityIssues.push({
        file: relativePath,
        issue: "innerHTML assignment (potential XSS)",
        severity: "MEDIUM",
      });
    }

    if (content.includes("dangerouslySetInnerHTML")) {
      securityIssues.push({
        file: relativePath,
        issue: "dangerouslySetInnerHTML usage",
        severity: "LOW",
      });
    }
  }

  return { usedPackages, consoleStatements, securityIssues };
}

/**
 * Find potentially unused dependencies
 */
function findUnusedDependencies(usedPackages) {
  const unused = [];

  for (const dep of dependencies) {
    if (!usedPackages.has(dep)) {
      unused.push(dep);
    }
  }

  return unused;
}

/**
 * Main analysis
 */
function runAnalysis() {
  const { usedPackages, consoleStatements, securityIssues } = analyzeImports();
  const unusedDeps = findUnusedDependencies(usedPackages);

  // Report results
  console.log("📊 ANALYSIS RESULTS");
  console.log("===================\n");

  // Dependencies
  console.log(
    `🔹 Dependencies: ${dependencies.length} total, ${usedPackages.size} used`
  );
  if (unusedDeps.length > 0) {
    console.log("❌ Potentially unused dependencies:");
    unusedDeps.forEach((dep) => console.log(`   - ${dep}`));
  } else {
    console.log("✅ All dependencies appear to be used");
  }
  console.log();

  // Console statements
  console.log(
    `🔹 Console statements: ${consoleStatements.reduce(
      (sum, item) => sum + item.count,
      0
    )} total`
  );
  if (consoleStatements.length > 0) {
    console.log("⚠️  Files with console statements:");
    consoleStatements
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .forEach((item) =>
        console.log(`   - ${item.file} (${item.count} statements)`)
      );
    if (consoleStatements.length > 10) {
      console.log(`   ... and ${consoleStatements.length - 10} more files`);
    }
  } else {
    console.log("✅ No console statements found");
  }
  console.log();

  // Security issues
  console.log(`🔹 Security issues: ${securityIssues.length} found`);
  if (securityIssues.length > 0) {
    const groupedBySeverity = securityIssues.reduce((acc, issue) => {
      acc[issue.severity] = acc[issue.severity] || [];
      acc[issue.severity].push(issue);
      return acc;
    }, {});

    for (const [severity, issues] of Object.entries(groupedBySeverity)) {
      console.log(`   ${severity} (${issues.length}):`);
      issues.forEach((issue) =>
        console.log(`   - ${issue.file}: ${issue.issue}`)
      );
    }
  } else {
    console.log("✅ No security issues found");
  }
  console.log();

  // Recommendations
  console.log("💡 RECOMMENDATIONS");
  console.log("==================\n");

  if (unusedDeps.length > 0) {
    console.log("1. Remove unused dependencies:");
    console.log(`   npm uninstall ${unusedDeps.join(" ")}`);
    console.log();
  }

  if (consoleStatements.length > 0) {
    console.log("2. Replace console statements with production logger");
    console.log("   Use prodLogger.debug() instead of console.log()");
    console.log();
  }

  if (securityIssues.length > 0) {
    console.log("3. Fix security issues:");
    console.log("   - Replace Function() with safe alternatives");
    console.log("   - Use DOMPurify for innerHTML operations");
    console.log("   - Review dangerouslySetInnerHTML usage");
    console.log();
  }

  console.log("4. Environment variables:");
  console.log("   - Copy .env.example to .env");
  console.log("   - Replace placeholder values with actual API keys");
  console.log();

  console.log("5. Build optimization:");
  console.log("   - Run npm run build to test production build");
  console.log("   - Check bundle size with source maps");
  console.log();
}

// Run the analysis
try {
  runAnalysis();
} catch (error) {
  console.error("❌ Analysis failed:", error.message);
  process.exit(1);
}
